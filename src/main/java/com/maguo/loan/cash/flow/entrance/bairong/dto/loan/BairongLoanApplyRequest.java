package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 百融放款申请——请求参数
 * @Date 2025/9/11 17:30
 * @Version v1.0
 **/
public class BairongLoanApplyRequest {
    /**
     * 用户id
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    /**
     * 渠道放款流水号
     */
    @NotBlank(message = "渠道放款流水号不能为空")
    private String outLoanSeq;
    /**
     * 申请期数
     */
    private Integer applyTnr;
    /**
     * 申请放款金额
     */
    private BigDecimal dnAmt;
    /**
     * 贷款用途
     */
    @NotBlank(message = "贷款用途不能为空")
    private String purpose;
    /**
     * 还款方式
     * SYS001	利随本清
     * SYS002	等额本息
     * SYS003	等额本金
     * SYS004	按期还息到期还本
     * SYS005	等额 本息(接收渠道还款计划)
     * SYS100	等本等息
     */
    private String mtdCde;
    /**
     * 放款账户信息
     */
    private AccountInfo accountInfo;

    public AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public void setAccountInfo(AccountInfo accountInfo) {
        this.accountInfo = accountInfo;
    }

    public class AccountInfo {
        /**
         * 账号类别
         */
        private String acctKind;
        /**
         * 账户开户行编码
         */
        private String acctBankCode;
        /**
         * 支付通道  TL-通联  YB-易宝
         */
        private String payChannel;
        /**
         * 开户人证件类型
         */
        private String idTyp;
        /**
         * 开户人证件号码
         */
        private String idNo;
        /**
         * 开户行预留电话
         */
        private String acctPhone;
        /**
         * 账号开户名
         */
        private String acctName;
        /**
         * 账号
         */
        private String acctNo;
        /**
         * 协议号
         */
        private String agreeNum;

        public String getAcctKind() {
            return acctKind;
        }

        public void setAcctKind(String acctKind) {
            this.acctKind = acctKind;
        }

        public String getAcctBankCode() {
            return acctBankCode;
        }

        public void setAcctBankCode(String acctBankCode) {
            this.acctBankCode = acctBankCode;
        }

        public String getPayChannel() {
            return payChannel;
        }

        public void setPayChannel(String payChannel) {
            this.payChannel = payChannel;
        }

        public String getIdTyp() {
            return idTyp;
        }

        public void setIdTyp(String idTyp) {
            this.idTyp = idTyp;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getAcctPhone() {
            return acctPhone;
        }

        public void setAcctPhone(String acctPhone) {
            this.acctPhone = acctPhone;
        }

        public String getAcctName() {
            return acctName;
        }

        public void setAcctName(String acctName) {
            this.acctName = acctName;
        }

        public String getAcctNo() {
            return acctNo;
        }

        public void setAcctNo(String acctNo) {
            this.acctNo = acctNo;
        }

        public String getAgreeNum() {
            return agreeNum;
        }

        public void setAgreeNum(String agreeNum) {
            this.agreeNum = agreeNum;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getMtdCde() {
        return mtdCde;
    }

    public void setMtdCde(String mtdCde) {
        this.mtdCde = mtdCde;
    }
}
