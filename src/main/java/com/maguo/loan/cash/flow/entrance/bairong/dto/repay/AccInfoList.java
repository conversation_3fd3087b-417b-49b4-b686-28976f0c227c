package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * @ClassName AccInfoList
 * <AUTHOR>
 * @Description 账号信息
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
@Data
public class AccInfoList {
    /**
     * 账户开户行编码
     */
    @NotBlank(message = "账户开户行编码不能为空")
    private String acctBankCode;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String acctNo;

    /**
     * 账号开户名
     */
    @NotBlank(message = "账号开户名不能为空")
    private String acctName;

    /**
     * 开户人证件类型
     */
    @NotBlank(message = "开户人证件类型不能为空")
    private String idTyp;

    /**
     * 开户人证件号码
     */
    @NotBlank(message = "开户人证件号码不能为空")
    private String idNo;

    /**
     * 开户行预留手机号
     */
    @NotBlank(message = "开户行预留手机号不能为空")
    @Pattern(regexp = "^[0-9]{11}$", message = "开户行预留手机号格式不正确（应为11位数字）")
    private String acctPhone;

}
