package com.maguo.loan.cash.flow.util;

import java.time.Duration;
import java.time.LocalDate;

/**
 * 基础常量类
 *
 * <AUTHOR>
 */
public class BaseConstants {

    /**
     * 身份证有效期长期
     */
    public static final LocalDate DEFAULT_LONG_CERT_END = LocalDate.of(9999, 12, 31);

    /**
     * 冷静期
     * 风控多少天后失效
     */
    public static final int REFUSE_DAY = 30;
    /**
     * 冻结期
     * 失败多久能再次申请风控和授信
     */
    public static final int FREEZE_DAY = 30;

    /**
     * 默认风控拒绝原因
     */
    public static final String DEFAULT_RISK_REJECT_REASON = "综合评分不足";


    /**
     * 锁默认释放时间
     */
    public static final Duration DEFAULT_LOCK_RELEASE_TIME = Duration.ofSeconds(8);

    /**
     * 订单默认超时关闭天数
     */
    public static final int DEFAULT_ORDER_CANCEL_DAYS = 15;


    /**
     * 失败原因最大长度
     */
    public static final int MAX_FAIL_REASON_LENGTH = 255;

}
