package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import java.math.BigDecimal;

/**
 * @ClassName BaiRongRepayTrialResponse
 * <AUTHOR>
 * @Description 还款试算请求响应参数
 * @Date 2025/9/11 16:51
 * @Version v1.0
 **/
public class BairongRepayTrialResponse {
    /**
     * 资金方放款流水号
     */
    private String loanSeq;

    /**
     * 资金方借据号
     */
    private String loanNo;

    /**
     * 还款总金额
     */
    private String totalAmt;

    /**
     * 应还本金
     */
    private String psRemPrcp;

    /**
     * 应还利息
     */
    private String odPrcpAmt;

    /**
     * 应还费用
     */
    private String odFeeAmt;

    /**
     * 应还违约金
     */
    private String penlAmt;

    /**
     * 应还罚息
     */
    private String odIntAmt;

    /**
     * 应还融担费
     */
    private BigDecimal odGrtAmt;

    /**
     * 应还复利
     */
    private BigDecimal odCommOdInt;

    /**
     * 应还其它项
     */
    private BigDecimal odOtherAmt;

    /**
     * 单笔限额
     */
    private String onceLimitAmt;

    /**
     * 日限额
     */
    private String dayLimitAmt;

    // 以下为getter和setter方法
    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getPsRemPrcp() {
        return psRemPrcp;
    }

    public void setPsRemPrcp(String psRemPrcp) {
        this.psRemPrcp = psRemPrcp;
    }

    public String getOdPrcpAmt() {
        return odPrcpAmt;
    }

    public void setOdPrcpAmt(String odPrcpAmt) {
        this.odPrcpAmt = odPrcpAmt;
    }

    public String getOdFeeAmt() {
        return odFeeAmt;
    }

    public void setOdFeeAmt(String odFeeAmt) {
        this.odFeeAmt = odFeeAmt;
    }

    public String getPenlAmt() {
        return penlAmt;
    }

    public void setPenlAmt(String penlAmt) {
        this.penlAmt = penlAmt;
    }

    public String getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(String odIntAmt) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getOdGrtAmt() {
        return odGrtAmt;
    }

    public void setOdGrtAmt(BigDecimal odGrtAmt) {
        this.odGrtAmt = odGrtAmt;
    }

    public BigDecimal getOdCommOdInt() {
        return odCommOdInt;
    }

    public void setOdCommOdInt(BigDecimal odCommOdInt) {
        this.odCommOdInt = odCommOdInt;
    }

    public BigDecimal getOdOtherAmt() {
        return odOtherAmt;
    }

    public void setOdOtherAmt(BigDecimal odOtherAmt) {
        this.odOtherAmt = odOtherAmt;
    }

    public String getOnceLimitAmt() {
        return onceLimitAmt;
    }

    public void setOnceLimitAmt(String onceLimitAmt) {
        this.onceLimitAmt = onceLimitAmt;
    }

    public String getDayLimitAmt() {
        return dayLimitAmt;
    }

    public void setDayLimitAmt(String dayLimitAmt) {
        this.dayLimitAmt = dayLimitAmt;
    }

}
