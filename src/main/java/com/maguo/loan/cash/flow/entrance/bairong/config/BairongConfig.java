package com.maguo.loan.cash.flow.entrance.bairong.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融配置类
 * @date 2025/9/10 14:44
 */
@Configuration
@ConfigurationProperties(prefix = "bairong.config")
public class BairongConfig {

    /**
     * 是否跳过签名验证
     */
    private boolean skipSignVerify = false;

    /**
     * AES密钥（jsonKey）
     */
    private String aesKey;

    /**
     * 渠道号码
     */
    private String channelCode;

    /**
     * 接入方系统ID
     */
    private String appId;

    /**
     * 签名密钥
     */
    private String signKey;

    // getter and setter methods
    public boolean isSkipSignVerify() {
        return skipSignVerify;
    }

    public void setSkipSignVerify(boolean skipSignVerify) {
        this.skipSignVerify = skipSignVerify;
    }

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSignKey() {
        return signKey;
    }

    public void setSignKey(String signKey) {
        this.signKey = signKey;
    }
}
