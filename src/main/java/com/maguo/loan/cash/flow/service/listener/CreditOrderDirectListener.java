package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @requirement: 授信放款新增直连改造
 * @description: 授信订单直连监听
 * @date 2025-8-25
 */
@Component
public class CreditOrderDirectListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(CreditApplyListener.class);

    @Autowired
    private OrderService orderService;

    public CreditOrderDirectListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_ORDER_DIRECT)
    public void listenCreditApply(Message message, Channel channel) {
        String orderId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听授信订单直连，orderId:{}", orderId);
            Order order = orderService.findById(orderId);
            if (order == null) {
                logger.warn("Order订单记录不存在, orderId: [{}]", orderId);
                return;
            }
            orderService.orderDirect(order);
        } catch (Exception e) {
            processException(orderId, message, e, "授信订单直连异常", getMqService()::submitCreditOrderDirectDelay);
        } finally {
            ackMsg(orderId, message, channel);
        }
    }
}
