package com.maguo.loan.cash.flow.entrance.bairong.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 加密响应数据
 * @date 2025/9/10 14:41
 */
public class BairongResponseData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道号码
     */
    private String channel;

    /**
     * 加密后的报文（AES加密后Base64编码）
     */
    private String json;

    /**
     * 接入方的系统id
     */
    private String appId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 随机字符串
     */
    private String randomStr;

    /**
     * 创建异常响应
     */
    public static BairongResponseData exception(String errorMsg, String channel, String appId) {
        BairongResponseData response = new BairongResponseData();
        response.setChannel(channel);
        response.setAppId(appId);
        response.setJson(errorMsg); // 异常情况下直接返回错误信息
        response.setRandomStr(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return response;
    }

    // getter and setter methods
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRandomStr() {
        return randomStr;
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    @Override
    public String toString() {
        return "EncryptedResponseData{" +
            "channel='" + channel + '\'' +
            ", json='" + json + '\'' +
            ", appId='" + appId + '\'' +
            ", sign='" + sign + '\'' +
            ", randomStr='" + randomStr + '\'' +
            '}';
    }
}
