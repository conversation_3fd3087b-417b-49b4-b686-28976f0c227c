package com.maguo.loan.cash.flow.repository.bairong;

import com.maguo.loan.cash.flow.entity.BairongRebindRecord;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/9/11 16:26
 */
public interface BairongRebindRecordRepository extends JpaRepository<BairongRebindRecord, String> {

    BairongRebindRecord findByCreditIdAndStateAndBoundSide(String creditId, ProcessState state, BoundSide boundSide);

}
