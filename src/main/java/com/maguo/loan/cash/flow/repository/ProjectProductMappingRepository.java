package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface ProjectProductMappingRepository extends JpaRepository<ProjectProductMapping, String> {

    Optional<ProjectProductMapping> findByProductCode(String productCode);

}

