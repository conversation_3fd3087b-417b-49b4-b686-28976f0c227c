package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongImageUploadRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongUploadResponse;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongImageUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/image/upload/BRYC")
public class BairongImageUploadController {

    @Autowired
    private BairongImageUploadService bairongImageUploadService;

    /**
     * 影像预上传接口
     *
     * @param request 影像上传请求
     * @return 上传结果
     */
    @PostMapping()
    public String uploadImage(@RequestBody BairongImageUploadRequest request) {
        BairongUploadResponse quotaQueryResponse = bairongImageUploadService.uploadImage(request);
        return quotaQueryResponse.getImageId();
    }
}
