package com.maguo.loan.cash.flow.entrance.common.strategy.lvxin;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.entrance.common.strategy.ppd.PpdFlowStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class LvxinFlowStrategy implements ProductCodeStrategy {
    private static final Logger logger = LoggerFactory.getLogger(PpdFlowStrategy.class);

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    @Override
    public boolean supports(String flowSource, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(flowSource) &&
            FlowChannel.LVXIN.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String productType = params.get("productType");
        if (StringUtils.isEmpty(productType)) {
            throw new IllegalArgumentException("绿信流量映射必须包含 'productType' 参数");
        }

        logger.info("------Lvxin----"+productType);
        String redisKey = MAPPING_KEY_PREFIX + productType;
        String projectCode = (String) cacheService.get(redisKey);

        if (StringUtils.isEmpty(projectCode)) {
            // 缓存中没有，从数据库查询
            Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(productType);
            if (!mapping.isPresent()) {
                throw new IllegalArgumentException("未找到对应的产品映射配置");
            }
            projectCode = mapping.get().getProjectCode();
            // 写入缓存，并设置过期时间
            cacheService.put(redisKey, projectCode, 24, TimeUnit.HOURS);
        }
        return projectCode;
    }
}
