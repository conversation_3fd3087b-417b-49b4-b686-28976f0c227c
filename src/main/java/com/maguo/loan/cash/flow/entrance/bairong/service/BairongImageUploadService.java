package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.common.util.DateUtil;
import com.maguo.loan.cash.flow.entity.BaiRongApplyRecord;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongImageUploadRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongUploadResponse;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongImageInfoEnum;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyRecordRepository;
import com.maguo.loan.cash.flow.service.FileService;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.UUID;

@Slf4j
@Service
public class BairongImageUploadService {

    private static final Logger logger = LoggerFactory.getLogger(BairongImageUploadService.class);
    @Value(value = "${oss.bucket.name}")
    private String ossBucket;
    @Autowired
    private FileService fileService;
    @Autowired
    private BairongApplyRecordRepository bairongApplyRecordRepository;

    /**
     * 执行影像上传
     *
     * @param request 上传请求
     * @return 上传结果
     */
    public BairongUploadResponse uploadImage(BairongImageUploadRequest request) {
        logger.info("开始执行百融影像上传，渠道流水号: {}, 影像类型: {}",
            request.getOutApplSeq(), request.getImgType());

        // 参数校验
        validateRequest(request);

        // 处理文件上传逻辑
        BairongUploadResponse response = processFileUpload(request);

        logger.info("百融影像上传完成，渠道流水号: {}, 影像ID: {}",
            request.getOutApplSeq(), request.getImageId());
        return response;
    }

    /**
     * 参数校验
     */
    private void validateRequest(BairongImageUploadRequest request) {
        logger.debug("开始参数校验，请求参数: {}", request);

        if (request == null) {
            logger.error("请求参数不能为空");
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (request.getOutApplSeq() == null || request.getOutApplSeq().trim().isEmpty()) {
            logger.error("渠道流水号不能为空");
            throw new IllegalArgumentException("渠道流水号不能为空");
        }

        if (request.getImgType() == null || request.getImgType().trim().isEmpty()) {
            logger.error("影像类型不能为空");
            throw new IllegalArgumentException("影像类型不能为空");
        }

        if (request.getApplyTime() == null || request.getApplyTime().trim().isEmpty()) {
            logger.error("申请时间不能为空");
            throw new IllegalArgumentException("申请时间不能为空");
        }

        if (request.getFileName() == null || request.getFileName().trim().isEmpty()) {
            logger.error("影像名称不能为空");
            throw new IllegalArgumentException("影像名称不能为空");
        }

        if (request.getFileBytes() == null || request.getFileBytes().length == 0) {
            logger.error("文件字节流不能为空");
            throw new IllegalArgumentException("文件字节流不能为空");
        }

        if (request.getImageId() == null || request.getImageId().trim().isEmpty()) {
            logger.error("影像Id不能为空");
            throw new IllegalArgumentException("影像Id不能为空");
        }

        logger.debug("参数校验通过");
    }

    /**
     * 处理文件上传
     */
    private BairongUploadResponse processFileUpload(BairongImageUploadRequest request) {
        logger.info("开始处理文件上传，渠道流水号: {}, 影像类型: {}",
            request.getOutApplSeq(), request.getImgType());

        BairongUploadResponse bairongUploadResponse = new BairongUploadResponse();
        BaiRongApplyRecord bairongApplyRecord = new BaiRongApplyRecord();
        //imageId 影像Id
        String imageId = generateImageId();
        bairongApplyRecord.setImageId(imageId);
        bairongApplyRecord.setOrderNo(request.getOutApplSeq());

        // 1.获取图片字节流
        byte[] fileBytes = request.getFileBytes();
        // 2.将图片字节流转换为输入流
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        // 3.解析后的图片文件存入阿里云oss
        // 3.1传过来身份证头像面
        if (request.getImgType().equals(BairongImageInfoEnum.ID_CARD_FRONT.getCode())){
            //3.2存入阿里云oss的文件路径和渠道流水号身份证头像面
            String headOssKey = uploadToOss(inputStream, ossBucket, "head", request.getOutApplSeq(), "jpg");
            // 4.将存入阿里云oss的文件路径和渠道流水号 存入百荣的数据库
            bairongApplyRecord.setIdPositive(headOssKey);
            bairongApplyRecordRepository.save(bairongApplyRecord);
        }else if (request.getImgType().equals(BairongImageInfoEnum.ID_CARD_BACK.getCode())){
            //身份证国徽面
            String headOssKey = uploadToOss(inputStream, ossBucket, "nation", request.getOutApplSeq(), "jpg");
            // 4.将存入阿里云oss的文件路径和渠道流水号 存入百荣的数据库
            bairongApplyRecord.setIdNegative(headOssKey);
            bairongApplyRecordRepository.save(bairongApplyRecord);
        }else if (request.getImgType().equals(BairongImageInfoEnum.FACE_RECOGNITION.getCode())){
            //人脸识别照
            String headOssKey = uploadToOss(inputStream, ossBucket, "livePhoto", request.getOutApplSeq(), "jpg");
            // 4.将存入阿里云oss的文件路径和渠道流水号 存入百荣的数据库
            bairongApplyRecord.setLivePhoto(headOssKey);
            bairongApplyRecordRepository.save(bairongApplyRecord);
        } else {
            logger.error("不支持的图片类型: {}, 渠道流水号: {}", request.getImgType(), request.getOutApplSeq());
            throw new IllegalArgumentException("不支持的图片类型: " + request.getImgType());
        }

        // 5.返回imageId 影像Id
        bairongUploadResponse.setImageId(imageId);
        logger.debug("文件上传处理完成，返回影像ID: {}", request.getImageId());
        return bairongUploadResponse;
    }

    private String uploadToOss(InputStream inputStream, String bucket, String picType, String openId, String imageType) {
        String picKey = generateOssPicKey(openId, picType, imageType);
        try {
            fileService.uploadOss(bucket, picKey, inputStream);
        } catch (Exception e) {
            logger.error("bairong info process pic error, openId: {}, fileType: {}", openId, picType);
            picKey = null;
        }
        return picKey;
    }

    private String generateOssPicKey(String openId, String prefix, String imageType) {
        String dayStr = DateUtil.formatShort(new Date());
        return "bairong/info/" + dayStr + "/" + openId + "/" + prefix + "_"
            + UUID.randomUUID().toString().replaceAll("-", "") + "." + imageType;
    }

    /**
     * 生成符合规则的imageId
     * 规则：BRIM+年月日时分秒+盐值
     * 保证大量数据下的唯一性
     * @return imageId
     */
    private String generateImageId() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 格式化为年月日时分秒
        String dateTimeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        // 生成纳秒部分作为额外唯一性保障
        String nanoTime = String.valueOf(System.nanoTime() % 1000000);
        // 生成UUID的一部分作为盐值
        String uuidPart = UUID.randomUUID().toString().replace("-", "").substring(0, 6);
        // 拼接imageId (BRIM + 年月日时分秒 + 纳秒部分 + UUID部分)
        return "BRIM" + dateTimeStr + nanoTime + uuidPart;
    }
}
