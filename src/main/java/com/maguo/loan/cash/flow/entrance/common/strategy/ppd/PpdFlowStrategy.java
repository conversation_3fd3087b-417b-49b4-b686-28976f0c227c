package com.maguo.loan.cash.flow.entrance.common.strategy.ppd;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class PpdFlowStrategy implements ProductCodeStrategy {

    private static final Logger logger = LoggerFactory.getLogger(PpdFlowStrategy.class);

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    @Autowired
    private CacheService cacheService;

    @Override
    public boolean supports(String source, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(source) &&
            FlowChannel.PPCJDL.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String sourceCode = params.get("sourceCode");
        String accessType = params.get("accessType");
        if (StringUtils.isEmpty(sourceCode) || StringUtils.isEmpty(accessType)) {
            throw new IllegalArgumentException("拍拍贷流量映射必须包含 'sourceCode' 和 'accessType' 参数");
        }
        String product = sourceCode + "_" + accessType;
        logger.info("------PPD----"+product);

        String redisKey = MAPPING_KEY_PREFIX + product;
        String projectCode = (String) cacheService.get(redisKey);

        if (StringUtils.isEmpty(projectCode)) {
            // 缓存中没有，从数据库查询
            Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(product);
            if (!mapping.isPresent()) {
                throw new IllegalArgumentException("未找到对应的产品映射配置");
            }
            projectCode = mapping.get().getProjectCode();
            // 写入缓存，并设置过期时间
            cacheService.put(redisKey, projectCode, 24, TimeUnit.HOURS);
        }
        return projectCode;
    }
}
