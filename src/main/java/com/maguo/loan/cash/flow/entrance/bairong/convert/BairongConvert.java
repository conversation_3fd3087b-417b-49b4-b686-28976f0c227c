package com.maguo.loan.cash.flow.entrance.bairong.convert;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.convert.ConvertMappings;
import com.maguo.loan.cash.flow.entity.BaiRongApplyBasicInfo;
import com.maguo.loan.cash.flow.entity.BaiRongApplyRecord;
import com.maguo.loan.cash.flow.entity.BaiRongLoanApply;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.common.covert.ProjectConfigMapperHelper;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.GioPushUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融数据转换
 * @date 2025/9/11 9:29
 */
@Mapper(imports = {LocalDateTime.class, Marriage.class, Industry.class, Relation.class, DateTimeFormatter.class, AgeUtil.class, GioPushUtil.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = ConvertMappings.class)
public interface BairongConvert {

    BairongConvert INSTANCE = Mappers.getMapper(BairongConvert.class);
    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;
    int THOUSAND = 1000;

    /**
     * 还款方式
     * SYS001	利随本清
     * SYS002	等额本息
     * SYS003	等额本金
     * SYS004	按期还息到期还本
     * SYS005	等额 本息(接收渠道还款计划)
     * SYS100	等本等息
     *
     * @param mtdCde 还款方式
     * @return 返回数据
     */
    public static Integer toMtdCdePub(String mtdCde) {
        return switch (mtdCde) {
            case "SYS002" -> 1;
            case "SYS003" -> 2;
            default -> 0;
        };
    }

    /**
     * 贷款用途字段映射
     * 70001	购买原材料
     * 70002	进货
     * 70003	购买设备
     * 70004	购买家具或家电
     * 70005	教育学习
     * 70006	个人或家庭消费
     * 70007	资金周转
     * 70010	其他
     * 70011	装修
     * 70012	旅游
     * 70013	婚庆
     * 70014	健康医疗
     *
     * @param loanPurpose 贷款用途
     * @return 返回数据
     */
    public static LoanPurpose toLoanPurpose(String loanPurpose) {
        return switch (loanPurpose) {
            case "70001", "70002", "70003", "70004", "70006" -> LoanPurpose.SHOPPING;
            case "70005" -> LoanPurpose.EDUCATION;
            case "70007", "70010" -> LoanPurpose.OTHER;
            case "70011" -> LoanPurpose.DECORATION;
            case "70012" -> LoanPurpose.TOUR;
            case "70013" -> LoanPurpose.MARRIAGE;
            case "70014" -> LoanPurpose.HEALTH;
            default -> LoanPurpose.OTHER;
        };
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "outLoanSeq", target = "outLoanSeq")
    @Mapping(source = "custName", target = "custName")
    @Mapping(source = "idTyp", target = "idTyp")
    @Mapping(source = "idNo", target = "idNo")
    @Mapping(source = "idNoStartDate", target = "idNoStartDate")
    @Mapping(source = "idNoEndDate", target = "idNoEndDate")
    @Mapping(source = "idOrgan", target = "idOrgan")
    @Mapping(expression = "java(IdCardUtil.getBirthDate(request.getIdNo()))", target = "bornDate")
    @Mapping(source = "indivMobile", target = "indivMobile")
    @Mapping(expression = "java(GioPushUtil.getGenderFromIDCard(request.getIdNo()))", target = "indivSex")
    @Mapping(expression = "java(AgeUtil.calculateAge(request.getIdNo()))", target = "apptAge")
    @Mapping(source = "indivMarital", target = "indivMarital")
    @Mapping(source = "indivEdu", target = "indivEdu")
    @Mapping(source = "idCardAddress", target = "idCardAddress")
    @Mapping(source = "nation", target = "nation")
    @Mapping(constant = "APPLICANT", target = "apptTyp")
    BaiRongApplyBasicInfo toBaiRongApplyBasicInfo(BairongLoanRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "request.outLoanSeq", target = "outLoanSeq")
    @Mapping(source = "basicInfoId", target = "applyBasicInfoId")
    @Mapping(source = "basicInfoId", target = "basicInfoId")
    @Mapping(expression = "java(LocalDate.now().format(DateTimeFormatter.ofPattern(\"yyyy-MM-dd\")))", target = "applyDt")
    @Mapping(source = "request.loanTyp", target = "loanTyp")
    @Mapping(source = "request.dnAmt", target = "dnAmt")
    @Mapping(source = "request.applyTnr", target = "applyTnr")
    @Mapping(source = "request.mtdCde", target = "mtdCde")
    @Mapping(source = "request.purpose", target = "purpose")
    @Mapping(source = "request.dueDayOpt", target = "dueDayOpt")
    @Mapping(constant = "Y", target = "directFlag")
    BaiRongLoanApply toBaiRongLoanApply(BairongLoanRequest request, String basicInfoId);

    @Mapping(source = "outLoanSeq", target = "partnerUserId")
    @Mapping(source = "custName", target = "name")
    @Mapping(source = "indivMobile", target = "phone")
    @Mapping(source = "idNo", target = "idCard")
    @Mapping(source = "dnAmt", target = "creditAmount")
    @Mapping(source = "applyTnr", target = "applyPeriod")

    @Mapping(source = "purpose", target = "baseInfo.loanPurpose")
    @Mapping(source = "indivEdu", target = "baseInfo.educational")
    @Mapping(source = "indivMarital", target = "baseInfo.maritalStatus")
    @Mapping(source = "idCardAddress", target = "baseInfo.liveAddress")
    @Mapping(source = "idOrgan", target = "authInfo.authority")
    @Mapping(source = "idNoStartDate", target = "authInfo.startDueTimeOcr")
    @Mapping(source = "idNoEndDate", target = "authInfo.endDueTimeOcr")
    @Mapping(source = "nation", target = "authInfo.nation")
    @Mapping(expression = "java(IdCardUtil.getGender(request.getIdNo()) == \"M\" ? \"男\" : \"女\")", target = "authInfo.sex")
    @Mapping(source = "idCardAddress", target = "authInfo.address")
    ApprovalRequest toApprovalRequest(BairongLoanRequest request);

    @Mapping(source = "approvalRequest.partnerUserId", target = "orderNo")
    //@Mapping(constant = "LVXIN", target = "flowChannel")
    @Mapping(source = "approvalRequest.userId", target = "openId")
    @Mapping(source = "approvalRequest.name", target = "name")
    @Mapping(source = "approvalRequest.phone", target = "mobile")
    @Mapping(source = "approvalRequest.idCard", target = "certNo")
    @Mapping(expression = "java(LocalDateTime.now())", target = "applyTime")
    @Mapping(constant = "INIT", target = "preOrderState")
    @Mapping(constant = "N", target = "isReject")
    @Mapping(constant = "SINGLE", target = "amountType")
    @Mapping(source = "approvalRequest.creditAmount", target = "applyAmount")
    @Mapping(source = "approvalRequest.applyPeriod", target = "applyPeriods")
    @Mapping(source = "approvalRequest.productType", target = "applyChannel", qualifiedByName = "toApplyChannel")
    @Mapping(constant = "Y", target = "isAssignBankChannel")
    @Mapping(source = "approvalRequest.productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    //@Mapping(source = "productType", target = "bankChannel", qualifiedByName = "toBankChannel")
    @Mapping(constant = "O", target = "equityRecipient")
    PreOrder toPreOrder(@MappingTarget PreOrder preOrder, BairongLoanRequest approvalRequest, ProjectConfigMapperHelper projectConfigMapperHelper);

    @AfterMapping
    default void completePreOrderMapping(@MappingTarget PreOrder preOrder, BairongLoanRequest request,
                                         ProjectConfigMapperHelper helper) {
        String projectCode = request.getProjectCode();
        if (projectCode != null && helper != null) {
            preOrder.setBankChannel(helper.getBankChannelFromProject(projectCode));
            preOrder.setFlowChannel(helper.getFlowChannelFromProject(projectCode));
        }
    }

    public static BankChannel toBankChannelPub(String productType) {
        return switch (productType) {
            case "01", "02" -> BankChannel.CYBK;
            case "03", "04" -> BankChannel.HXBK;
            default -> null;
        };
    }

    @Mapping(source = "partnerUserId", target = "orderNo")
    @Mapping(source = "baseInfo.email", target = "email")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(source = "idCard", target = "idCardNo")
    @Mapping(source = "authInfo.frontUrl", target = "idPositive")
    @Mapping(source = "authInfo.backUrl", target = "idNegative")
    @Mapping(source = "authInfo.sex", target = "idSex")
    @Mapping(source = "authInfo.nation", target = "idEthnic")
    @Mapping(source = "authInfo.authority", target = "idIssueOrg")
    @Mapping(source = "authInfo.startDueTimeOcr", target = "idStartTime")
    @Mapping(source = "authInfo.endDueTimeOcr", target = "idEndTime")
    @Mapping(source = "authInfo.address", target = "idAddress")
    @Mapping(source = "authInfo.borrower", target = "livePhoto")
    @Mapping(source = "authInfo.liveRate", target = "faceScore")
    @Mapping(source = "authInfo.facialSupplier", target = "facialSupplier")
    @Mapping(source = "baseInfo.loanPurpose", target = "loanPurpose")
    @Mapping(source = "baseInfo.educational", target = "education")
    @Mapping(source = "baseInfo.inCome", target = "monthlyIncome")
    @Mapping(source = "baseInfo.workType", target = "job")
    @Mapping(source = "baseInfo.companyName", target = "workUnitName", qualifiedByName = "toCompanyName")
    @Mapping(source = "baseInfo.companyAddress", target = "workUnitAddress")
    @Mapping(source = "baseInfo.companyProvinceCode", target = "workUnitProvinceCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyCityCode", target = "workUnitCityCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyAreaCode", target = "workUnitAreaCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.maritalStatus", target = "marriage")
    @Mapping(source = "deviceInfo.gpsLng", target = "longitude")
    @Mapping(source = "deviceInfo.gpsLat", target = "latitude")
    @Mapping(source = "baseInfo.industry", target = "industry")
    @Mapping(source = "contactInfos", target = "relations", qualifiedByName = "toRelationJsonArrayStr")
    @Mapping(source = "deviceInfo", target = "deviceInfo", qualifiedByName = "toJsonStr")
    @Mapping(source = "baseInfo.liveAddress", target = "livingAddress")
    @Mapping(source = "baseInfo.liveProvince", target = "livingProvince")
    @Mapping(source = "baseInfo.liveCity", target = "livingCity")
    @Mapping(source = "baseInfo.liveArea", target = "livingArea")
    @Mapping(source = "baseInfo.provinceCode", target = "livingProvinceCode")
    @Mapping(source = "baseInfo.cityCode", target = "livingCityCode")
    @Mapping(source = "baseInfo.areaCode", target = "livingAreaCode")
    @Mapping(source = "productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    @Mapping(constant = "O", target = "equityRecipient")
    BaiRongApplyRecord toBaiRongApplyRecord(BairongLoanRequest approvalRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "partnerUserId", target = "orderNo")
    @Mapping(source = "baseInfo.email", target = "email")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(source = "idCard", target = "idCardNo")
    @Mapping(source = "authInfo.frontUrl", target = "idPositive")
    @Mapping(source = "authInfo.backUrl", target = "idNegative")
    @Mapping(source = "authInfo.sex", target = "idSex")
    @Mapping(source = "authInfo.nation", target = "idEthnic")
    @Mapping(source = "authInfo.authority", target = "idIssueOrg")
    @Mapping(source = "authInfo.startDueTimeOcr", target = "idStartTime")
    @Mapping(source = "authInfo.endDueTimeOcr", target = "idEndTime")
    @Mapping(source = "authInfo.address", target = "idAddress")
    @Mapping(source = "authInfo.borrower", target = "livePhoto")
    @Mapping(source = "authInfo.liveRate", target = "faceScore")
    @Mapping(source = "authInfo.facialSupplier", target = "facialSupplier")
    @Mapping(source = "baseInfo.loanPurpose", target = "loanPurpose")
    @Mapping(source = "baseInfo.educational", target = "education")
    @Mapping(source = "baseInfo.inCome", target = "monthlyIncome")
    @Mapping(source = "baseInfo.workType", target = "job")
    @Mapping(source = "baseInfo.companyName", target = "workUnitName", qualifiedByName = "toCompanyName")
    @Mapping(source = "baseInfo.companyAddress", target = "workUnitAddress")
    @Mapping(source = "baseInfo.companyProvinceCode", target = "workUnitProvinceCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyCityCode", target = "workUnitCityCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyAreaCode", target = "workUnitAreaCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.maritalStatus", target = "marriage")
    @Mapping(source = "deviceInfo.gpsLng", target = "longitude")
    @Mapping(source = "deviceInfo.gpsLat", target = "latitude")
    @Mapping(source = "baseInfo.industry", target = "industry")
    @Mapping(source = "contactInfos", target = "relations", qualifiedByName = "toRelationJsonArrayStr")
    @Mapping(source = "deviceInfo", target = "deviceInfo", qualifiedByName = "toJsonStr")
    @Mapping(source = "productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    @Mapping(constant = "O", target = "equityRecipient")
    BaiRongApplyRecord toBaiRongApplyRecord(@MappingTarget BaiRongApplyRecord baiRongApplyRecord, BairongLoanRequest approvalRequest);

    /**
     * 还款试算调用资方后部分响应字段转换
     * totalAmt 还款总金额 = 还款总金额(元)amount;
     * psRemPrcp	应还本金 = 还款本金(元) principal;
     * odPrcpAmt	应还利息	= 还款利息(元) interest;
     * odIntAmt	应还罚息	= 还款罚息(元) penalty;
     * penlAmt	应还违约金 = 还款提结违约金(元) breachFee;
     * odFeeAmt	应还费用 = 还款手续费(元)consultFee;
     * odGrtAmt	应还融担费 = 融担费 guaranteeFee;
     */
    @Mappings({
        @Mapping(source = "amount", target = "totalAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "principal", target = "psRemPrcp", qualifiedByName = "safeAmount"),
        @Mapping(source = "interest", target = "odPrcpAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "penalty", target = "odIntAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "breachFee", target = "penlAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "consultFee", target = "odFeeAmt", qualifiedByName = "safeAmount"),
        @Mapping(source = "guaranteeFee", target = "odGrtAmt", qualifiedByName = "safeAmount")
    })
    BairongRepayTrialResponse toRepayTrailRes(TrialResultVo trialResultVo);

}
