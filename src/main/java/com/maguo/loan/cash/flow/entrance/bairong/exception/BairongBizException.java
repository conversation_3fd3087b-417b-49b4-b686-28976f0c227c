package com.maguo.loan.cash.flow.entrance.bairong.exception;


/**
 * 异常类
 */
public class BairongBizException extends RuntimeException {

    private final BairongResultCode resultCode;

    public BairongBizException(String message) {
        super(message);
        this.resultCode = BairongResultCode.SYSTEM_ERROR;
    }

    public BairongBizException(BairongResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public BairongBizException(String message, BairongResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public BairongResultCode getResultCode() {
        return resultCode;
    }

}
