package com.maguo.loan.cash.flow.entrance.common.strategy;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class ProjectCodeMapper {

    private static final Logger logger = LoggerFactory.getLogger(ProjectCodeMapper.class);

    private final List<ProductCodeStrategy> strategies;

    @Autowired
    private ProductCodeStrategyFactory strategyFactory;

    private static final String CACHE_INIT_FLAG_KEY = "project:mappings:initialized";

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    @Autowired
    public ProjectCodeMapper(List<ProductCodeStrategy> strategies) {
        this.strategies = strategies;
    }

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    @Autowired
    private CacheService cacheService;

    @PostConstruct
    public void init() {
        logger.info("================= ProjectCodeMapper 诊断信息 =================");
        if (this.strategies == null || this.strategies.isEmpty()) {
            logger.error("【严重】: strategies 列表为空或null！没有注入任何策略Bean！");
        } else {
            logger.info("成功注入了 {} 个策略Bean，列表如下:", this.strategies.size());
            this.strategies.forEach(strategy -> {
                logger.info(" -> 已加载策略: {}", strategy.getClass().getName());
            });
        }
        // 先判断“初始化标记”是否存在
        if (cacheService.existKey(CACHE_INIT_FLAG_KEY)) {
            logger.info("初始化标记 '{}' 已存在，跳过从数据库加载数据。", CACHE_INIT_FLAG_KEY);
            return;
        }
        logger.info("初始化标记不存在，开始从数据库加载映射关系到 Redis...");

        List<ProjectProductMapping> allMappings = projectProductMappingRepository.findAll();

        if (allMappings.isEmpty()) {
            logger.warn("数据库中没有 '产品-项目' 映射数据，仍将设置初始化标记。");
        } else {
            // 遍历数据并逐条写入 Redis
            allMappings.forEach(mapping -> {
                String redisKey = MAPPING_KEY_PREFIX + mapping.getProductCode();
                String projectCode = mapping.getProjectCode();
                cacheService.put(redisKey, projectCode);
            });
            logger.info("成功将 {} 条映射关系加载到 Redis。", allMappings.size());
        }

        cacheService.put(CACHE_INIT_FLAG_KEY, "true", 24, TimeUnit.HOURS);
        logger.info("Redis 缓存初始化标记 '{}' 设置成功，有效期24小时。", CACHE_INIT_FLAG_KEY);
    }


    /**
     * @param source 映射来源
     * @param params 包含所有相关特征的键值对
     * @return 匹配到的 project_code
     */
    public String getProjectCode(String source, Map<String, String> params) {
        return strategyFactory.getProductCode(source, params);
    }


}
