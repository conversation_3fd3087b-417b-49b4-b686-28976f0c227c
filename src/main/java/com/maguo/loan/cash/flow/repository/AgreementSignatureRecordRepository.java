package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface AgreementSignatureRecordRepository extends JpaRepository<AgreementSignatureRecord, String> {
    List<AgreementSignatureRecord> findByUserIdAndLoanStage(String userId, LoanStage stage);

    List<AgreementSignatureRecord> findBySignState(ProcessState processing);

    List<AgreementSignatureRecord> findByIdIn(List<String> signApplyIdList);

    List<AgreementSignatureRecord> findBySignStateAndCreatedTimeBetween(ProcessState signState, LocalDateTime beginTime, LocalDateTime endTime);

    List<AgreementSignatureRecord> findByCreatedTimeBetweenAndSignStateIn(LocalDateTime beginTime, LocalDateTime endTime, ProcessState... signStates);

    List<AgreementSignatureRecord> findByCreatedTimeBetweenAndRemark(LocalDateTime beginTime, LocalDateTime endTime, String remark);

    @Query(value = "SELECT s.id FROM Order o "
        + " LEFT JOIN AgreementSignRelation a on o.riskId = a.relatedId"
        + " LEFT JOIN AgreementSignatureRecord s on a.signApplyId = s.id"
        + " where  o.orderState = ?4 and o.createdTime >= ?1"
        + " and o.createdTime <= ?2 and s.fileType = ?3"
    )
    List<String> findByCreatedTimeBetweenAndFileType(LocalDateTime beginTime, LocalDateTime endTime, FileType fileType,
                                                     OrderState orderState);

    // 根据risk_id 和 sign_state 查询list
    List<AgreementSignatureRecord> findByRiskIdAndSignState(String riskId, ProcessState signState);

    @Query(value = "SELECT asr.* " +
        "FROM agreement_signature_record asr " +
        "INNER JOIN ( " +
        "    SELECT risk_id FROM pre_order WHERE order_no = :orderNo " +
        "    UNION ALL " +
        "    SELECT risk_id FROM `order` WHERE outer_order_id = :orderNo " +
        ") combined ON asr.risk_id = combined.risk_id " +
        "WHERE asr.sign_state = :signState",
        nativeQuery = true)
    List<AgreementSignatureRecord> findByOrderNoAndSignState(@Param("orderNo") String orderNo, @Param("signState") String signState);

    AgreementSignatureRecord findTopByRiskIdAndSignStateAndFileTypeOrderByCreatedTimeDesc(String riskId, ProcessState signState, FileType fileType);

    @Query(value = "SELECT asr.* " +
        "FROM agreement_signature_record asr " +
        "INNER JOIN ( " +
        "    SELECT risk_id FROM pre_order WHERE order_no = :orderNo " +
        "    UNION ALL " +
        "    SELECT risk_id FROM `order` WHERE outer_order_id = :orderNo " +
        ") source_risks ON asr.risk_id = source_risks.risk_id " +
        "WHERE asr.sign_state = 'SUCCEED' " +
        "AND asr.file_type = :fileType " +
        "ORDER BY asr.created_time DESC " +
        "LIMIT 1",
        nativeQuery = true)
    Optional<AgreementSignatureRecord> findLatestAgreementSignatureRecordByOrderNoAndFileType(
        @Param("orderNo") String orderNo,
        @Param("fileType") String fileType);

}
