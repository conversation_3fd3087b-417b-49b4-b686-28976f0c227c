package com.maguo.loan.cash.flow.entrance.ppd.filter;

import com.alibaba.fastjson2.JSONObject;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.entrance.ppd.common.BizException;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonResponse;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdErrorCode;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdServiceId;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.utils.PpdRsaUtil;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

public class EncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(EncryptFilter.class);

    private PpdConfig config;

    public EncryptFilter(PpdConfig ppdConfig) {
        this.config = ppdConfig;
    }

    static String channelA = "";
    static String channelB = "";
    static String serviceId = "";
    static String seqNo = "";

    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("拍拍贷 入参原始报文: {}, 请求地址: {}, 跳过验签: {}", requestStr, req.getRequestURL(), config.getSkipSignVerify());

        String channel = null;
        boolean aTrue = false;
        try {
            PpdCommonRequest requestData = JsonUtil.convertToObject(requestStr, PpdCommonRequest.class);
            channelA = requestData.getChannelA();
            channelB = requestData.getChannelB();
            serviceId = requestData.getServiceId();
            seqNo = requestData.getSeqNo();
            // 兼容测试
            aTrue = StringUtils.equals("true", requestData.getSign());
            if (!config.getSkipSignVerify() && !aTrue) {
                channel = requestData.getChannelB();
                //检查参数有效性
                check(requestData);

                //验签
                boolean checkSign = PpdRsaUtil.verify(requestData, config.getClientPublicKey());
                if (!checkSign) {
                    logger.error("{} 验签失败,{}", channel, req.getRequestURL());
                    throw new PpdBizException(PpdResultCode.SIGN_VERIFY_FAIL);
                }
                try {
                    //业务数据解密
                    String decrypted = PpdRsaUtil.decryptRequest(requestData.getData(), config.getServerPrivateKey());
                    requestData.setData(decrypted);
                    logger.info("{} 入参解密后报文: {},{}", channel, JsonUtil.convertToString(requestData), req.getRequestURL());
                    req.setAttribute("decryptedRequestBody", decrypted);

                } catch (Exception e) {
                    logger.error("{} 解密失败,{}", channel, e);
                    throw new PpdBizException(PpdResultCode.SIGN_VERIFY_FAIL);
                }
            } else {
                req.setAttribute("decryptedRequestBody", requestData.getData());
            }

            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, requestData.getData().getBytes(StandardCharsets.UTF_8));
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);
            chain.doFilter(requestWrapper, responseWrapper);

            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            byte[] contentBytes = responseWrapper.getContentAsByteArray();
            logger.info("{} 出参原始报文: {}, {}", channel, new String(contentBytes, StandardCharsets.UTF_8), req.getRequestURL());
            String content = new String(contentBytes, StandardCharsets.UTF_8);

            PpdCommonResponse response = assembleSuccessResponse(requestData, content);

            if (!config.getSkipSignVerify() && !aTrue) {
                //没有业务返回数据就不需要加签、加密
                if (contentBytes.length > 0) {
                    //响应data加密、加签
                    PpdRsaUtil.encryptResponse(response, config.getClientPublicKey(), config.getServerPrivateKey());
                }
            }

            String responseStr = JSONObject.toJSONString(response);

            logger.info("{} 出参加密后报文: {}", channel, responseStr);
            contentBytes = responseStr.getBytes(StandardCharsets.UTF_8);

            res.setContentLength(contentBytes.length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(contentBytes);
            outputStream.flush();

        } catch (Exception e) {
            logger.error("{} 调用异常,{}", channel, req.getRequestURL(), e);
            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            PpdCommonResponse response = assembleFailResponse(e);
            if (!config.getSkipSignVerify() && !aTrue) {
                //响应data加密、加签
                PpdRsaUtil.encryptResponse(response, config.getClientPublicKey(), config.getServerPrivateKey());
            }

            String responseStr = JSONObject.toJSONString(response);
            logger.info("{} 出参加密后报文: {}", channel, responseStr);

            byte[] contentBytes = JsonUtil.toJsonString(response).getBytes(StandardCharsets.UTF_8);
            res.setContentLength(contentBytes.length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(contentBytes);
            outputStream.flush();
        }
    }

    private static PpdCommonResponse assembleFailResponse(Exception e) {
        PpdCommonResponse response = new PpdCommonResponse();
        response.setChannelA(channelA);
        response.setChannelB(channelB);
        response.setServiceId(serviceId);
        response.setSeqNo(seqNo);
        String code = PpdResultCode.SYSTEM_ERROR.getCode();
        String msg = e.getMessage();
        response.setData("");

        if (e.getCause() instanceof RuntimeException) {
            code = PpdResultCode.SYSTEM_ERROR.getCode();
            msg = PpdResultCode.SYSTEM_ERROR.getMsg();
        }

        if (e.getCause() instanceof BizException) {
            BizException ex = (BizException) e.getCause();
            code = ex.getResultCode().getCode();
            msg = ex.getMessage();
        }

        if (e.getCause() instanceof ValidationException) {
            ValidationException ex = (ValidationException) e.getCause();
            code = ex.getResultCode().getCode();
            msg = ex.getMessage();
        }

        if (e.getCause() instanceof PpdBizException) {
            PpdBizException ex = (PpdBizException) e.getCause();
            //TODO： 业务失败，returnCode应为成功
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errCode", PpdErrorCode.ER00.name());
            jsonObject.put("msg", ex.getMessage());
            jsonObject.put("status", "01");
            response.setData(jsonObject.toString());
            code = PpdResultCode.SUCCESS.getCode();
            msg = PpdResultCode.SUCCESS.getMsg();
        }

        if (e.getCause() instanceof LvxinBizException) {
            LvxinBizException ex = (LvxinBizException) e.getCause();
            code = ex.getResultCode().getCode();
            msg = ex.getMessage();
        }

        if (msg != null && msg.contains("maguo")) {
            // 屏蔽内部重要信息
            msg = msg.replaceAll("maguo", "***").replaceAll("绿信", "拍拍");
        }
        response.setStatus("01");
        response.setTransDate(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        response.setTransTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmssSSS")));
        response.setReturnCode(StringUtils.defaultIfBlank(code, PpdResultCode.SYSTEM_ERROR.getCode()));
        response.setReturnMsg(StringUtils.defaultIfBlank(msg, PpdResultCode.SYSTEM_ERROR.getMsg()));
        return response;
    }

    private static PpdCommonResponse assembleSuccessResponse(PpdCommonRequest requestData, String content) {
        PpdCommonResponse response = new PpdCommonResponse();
        response.setChannelA(requestData.getChannelA());
        response.setChannelB(requestData.getChannelB());
        response.setServiceId(requestData.getServiceId());
        response.setSeqNo(requestData.getSeqNo());

        response.setTransDate(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        response.setTransTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmssSSS")));
        response.setReturnCode(PpdResultCode.SUCCESS.getCode());
        response.setReturnMsg(PpdResultCode.SUCCESS.getMsg());
        response.setData(content);
        return response;
    }

    private void check(PpdCommonRequest requestData) {
        Arrays.stream(FlowChannel.values()).filter(c -> c.name().equals(requestData.getChannelB())).findAny().orElseThrow(
            () -> new PpdBizException(PpdResultCode.NOT_SUPPORT_CHANNEL));

        Arrays.stream(PpdServiceId.values()).filter(c -> c.getServiceId().equals(requestData.getServiceId())).findAny().orElseThrow(
            () -> new PpdBizException(PpdResultCode.NOT_SUPPORT_SERVICE));
    }

}
