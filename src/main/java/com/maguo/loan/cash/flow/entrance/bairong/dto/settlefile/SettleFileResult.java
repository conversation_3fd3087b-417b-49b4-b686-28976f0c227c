package com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile;

import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongSettleFileStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @title: 结清证明下载响应
 * @desc:
 * @date 2025/9/11 10:13
 */
public class SettleFileResult {
    /**
     * 结果描述
     * 11001-开具处理中
     * 11002-开具成功
     * 11003-开具失败
     */
    private String status;
    /**
     * 失败原因
     */
    private String remark;
    /**
     * 11002-开具成功时，内容不为空
     */
    private String fileData;

    /**
     * 文件名
     */
    private String fileName;


    public static SettleFileResult fail(String errMsg) {
        SettleFileResult result = new SettleFileResult();
        result.setStatus(BairongSettleFileStatus.FAIL.getStatus());
        result.setRemark(errMsg);
        return result;
    }

    public static SettleFileResult process(String message) {
        SettleFileResult result = new SettleFileResult();
        result.setStatus(BairongSettleFileStatus.PROCESSING.getStatus());
        result.setRemark(message);
        return result;
    }

    public static SettleFileResult success() {
        SettleFileResult result = new SettleFileResult();
        result.setStatus(BairongSettleFileStatus.SUCCESS.getStatus());
        result.setRemark(BairongSettleFileStatus.SUCCESS.getDesc());
        return result;
    }

    public static SettleFileResult success(String fileName, String fileData) {
        SettleFileResult result = new SettleFileResult();
        result.setStatus(BairongSettleFileStatus.SUCCESS.getStatus());
        result.setRemark(BairongSettleFileStatus.SUCCESS.getDesc());
        result.setFileName(fileName);
        result.setFileData(fileData);
        return result;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFileData() {
        return fileData;
    }

    public void setFileData(String fileData) {
        this.fileData = fileData;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
