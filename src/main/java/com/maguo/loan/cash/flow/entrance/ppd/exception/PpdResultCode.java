package com.maguo.loan.cash.flow.entrance.ppd.exception;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 公共响应码
 * @Date 2024/3/21 14:27
 * @Version v1.0
 **/
public enum PpdResultCode {

    SUCCESS("0000", "请求成功"),
    SYSTEM_ERROR("1000", "请求异常（未知错误）"),
    INVALID_PARAM("4001", "参数错误"),
    SIGN_VERIFY_FAIL("4002", "验签失败"),
    NOT_SUPPORT_CHANNEL("4003", "未知渠道号（channlB）"),
    NOT_SUPPORT_SERVICE("4004", "未知服务号（serviceId）");

    private String code;
    private String msg;

    PpdResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public PpdResultCode getByCode(String code) {
        for (PpdResultCode resultCode : PpdResultCode.values()) {
            if (Objects.equals(resultCode.getCode(), code)) {
                return resultCode;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
