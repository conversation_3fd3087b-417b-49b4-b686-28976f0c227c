package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

/**
 * <AUTHOR>
 * @Description 放款申请响应
 * @Date 2025/9/11 17:35
 * @Version v1.0
 **/
public class BairongLoanApplyResponse {

    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;
    /**
     * 资金方授信流水号
     */
    private String applCde;
    /**
     * 资金方放款流水号
     */
    private String loanSeq;
    /**
     * 资金方借据号
     */
    private String loanNo;
    /**
     * 放款状态  (100:放款中; 200:放款成功; 300:放款失败)
     */
    private String dnSts;
    /**
     * 放款描述 (放款失败原因)
     */
    private String payMsg;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getDnSts() {
        return dnSts;
    }

    public void setDnSts(String dnSts) {
        this.dnSts = dnSts;
    }

    public String getPayMsg() {
        return payMsg;
    }

    public void setPayMsg(String payMsg) {
        this.payMsg = payMsg;
    }

    public static BairongLoanApplyResponse failure(String outLoanSeq, String dnSts, String payMsg) {
        BairongLoanApplyResponse response = new BairongLoanApplyResponse();
        response.setOutLoanSeq(outLoanSeq);
        response.setDnSts(dnSts);
        response.setPayMsg(payMsg);
        return response;
    }
}
