package com.maguo.loan.cash.flow.job.agreement;

import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.fql.FqlLoanAgreementJob;
import com.maguo.loan.cash.flow.job.ppd.PPDSignJob;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@JobHandler("supplementaryAgreementJob")
public class SupplementaryAgreementJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(LoanAgreementJob.class);
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private PPDSignJob ppdSignJob;
    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;
    @Autowired
    private FqlLoanAgreementJob fqlLoanAgreementJob;

    @Override
    public void doJob(JobParam jobParam) {
        List<Loan> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("supplementaryAgreementJob loanIds size:{}", jobParam.getLoanIds().size());
            //根据借据id数组查询
            list = loanRepository.findAllById(jobParam.getLoanIds());
        }
        if (CollectionUtils.isEmpty(list)) {
            logger.info("supplementaryAgreementJob 补协议");
            return;
        }
        for (Loan loan : list) {
            //复借场景需查询客户首借时传输给资金方的协议（综合授权书、数字证书使用授权协议 ），复制一份到复借对应的授信编号的路径下
            Order order = orderRepository.findOrderById(loan.getOrderId());
            //获取流量系统文件
            // 这里根据riskId查询AgreementSignatureRecord表流量签署的协议
            List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByOrderNoAndSignState(order.getOuterOrderId(), ProcessState.SUCCEED.name());
            if (CollectionUtil.isNotEmpty(agreementList)) {
                for (AgreementSignatureRecord record : agreementList) {
                    logger.info("复借时，流量系统协议文件上传sftp：根据项目唯一编码" + loan.getProjectCode() + "和文件类型" + record.getFileType().name() + "查询合同模板配置信息");
                    //根据项目唯一编码和文件类型查询合同模板配置信息
                    ProjectAgreementDto agreementDto = projectAgreementFeign.getByStageAndType(loan.getProjectCode(), null, null, record.getFileType().name());
                    if(Objects.nonNull(agreementDto)){
                        String flowContractName = agreementDto.getFlowContractName();//流量方合同名称
                        logger.info("====合同模板配置的合同名称为：" + flowContractName);
                        if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {//绿信
                            logger.info("====复借时，绿信流量系统协议文件上传sftp====");
                            loanAgreementJob.uploadFlowAgreementFileToSftp(loan, record.getCommonOssUrl(), record.getFileType(), order,flowContractName);
                        }
                        if (FlowChannel.PPCJDL.equals(loan.getFlowChannel())) {//拍拍
                            logger.info("====复借时，拍拍流量系统协议文件上传sftp====");
                            ppdSignJob.uploadFlowFileToSftp(loan, record.getCommonOssUrl(), flowContractName);
                        }
                        if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())) {//分期乐
                            logger.info("====复借时，分期乐流量系统协议文件上传sftp====");
                            String bizDateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                            //fqlLoanAgreementJob.uploadSignFileToSftp(loan, order, record.getCommonOssUrl(), flowContractName, bizDateStr);
                        }
                        logger.info("协议文件上传sftp成功");
                    }
                }
            }
            loanAgreementJob.handleReloanProtocolFiles(loan);
            // 获取资金系统签署完成与资方返回的协议
            List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(loan.getUserId(), loan.getId());
            //上传
            logger.info("获取资金系统签署完成与资方返回的协议:{}", JsonUtil.toJsonString(userFileList));
            if (CollectionUtil.isNotEmpty(userFileList)) {
                for (UserFile userFile : userFileList) {
                    if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        continue;
                    }
                    logger.info("复借时，资金系统协议文件上传sftp：根据项目唯一编码" + loan.getProjectCode() + "和文件类型" + userFile.getFileType().name() + "查询合同模板配置信息");
                    //根据项目唯一编码和文件类型查询合同模板配置信息
                    ProjectAgreementDto agreementDto = projectAgreementFeign.getByStageAndType(loan.getProjectCode(), null, null, userFile.getFileType().name());
                    if(Objects.nonNull(agreementDto)){
                        String capitalContractName = agreementDto.getCapitalContractName();//资金方合同名称
                        logger.info("====合同模板配置的合同名称为：" + capitalContractName);
                        if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {//绿信
                            logger.info("====复借时，绿信资金系统协议文件上传sftp====");
                            loanAgreementJob.uploadCoreAgreementFileToSftp(loan, userFile, order, capitalContractName);
                        }
                        if (FlowChannel.PPCJDL.equals(loan.getFlowChannel())) {//拍拍
                            logger.info("====复借时，拍拍资金系统协议文件上传sftp====");
                            ppdSignJob.uploadCapitalFileToSftp(loan, userFile, capitalContractName);
                        }
                        if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())) {//分期乐
                            logger.info("====复借时，分期乐资金系统协议文件上传sftp====");
                            String bizDateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                            fqlLoanAgreementJob.uploadSignFileToSftp(loan, order, userFile, capitalContractName, bizDateStr);
                        }
                        logger.info("协议文件上传sftp成功");
                    }
                }
            }
        }
    }

}
