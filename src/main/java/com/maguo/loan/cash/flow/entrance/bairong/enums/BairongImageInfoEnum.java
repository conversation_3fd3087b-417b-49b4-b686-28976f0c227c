package com.maguo.loan.cash.flow.entrance.bairong.enums;

/**
 * 影像信息枚举类
 */
public enum BairongImageInfoEnum {

    /**
     * 身份证头像面
     */
    ID_CARD_FRONT("1", "idCardFront", "身份证头像面"),

    /**
     * 身份证国徽面
     */
    ID_CARD_BACK("2", "idCardBack", "身份证国徽面"),

    /**
     * 人脸识别照
     */
    FACE_RECOGNITION("3", "faceRecognition", "人脸识别照");


    private String name;
    private String code;
    private String desc;

    BairongImageInfoEnum(String code, String desc, String name) {
        this.code = code;
        this.desc = desc;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
