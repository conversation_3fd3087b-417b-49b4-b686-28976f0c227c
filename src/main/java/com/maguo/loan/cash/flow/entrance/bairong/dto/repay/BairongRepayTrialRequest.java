package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * @ClassName BaiRongRepayTrialRequest
 * <AUTHOR>
 * @Description 还款试算请求
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
public class BairongRepayTrialRequest {
    /**
     * 资金方放款流水号
     */
    private String loanSeq;
    /**
     * 资金方借据号
     */
    @NotBlank(message = "资金方借据号不能为空")
    private String loanNo;
    /**
     * 还款模式
     */
    @NotBlank(message = "还款模式不能为空")
    private String repaymentMode;
    /**
     * 还款期数
     */
    @NotBlank(message = "还款期数不能为空")
    private String period;
    /**
     * 操作时间
     */
    @NotBlank(message = "操作时间不能为空")
    private String operateTime;
    /**
     * 客户实际还款日期
     */
    private String custSetlDt;

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getRepaymentMode() {
        return repaymentMode;
    }

    public void setRepaymentMode(String repaymentMode) {
        this.repaymentMode = repaymentMode;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getCustSetlDt() {
        return custSetlDt;
    }

    public void setCustSetlDt(String custSetlDt) {
        this.custSetlDt = custSetlDt;
    }

}
