package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName BaiRongRepayResultRequest
 * <AUTHOR>
 * @Description 还款结果查询请求参数
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
@Data
public class BairongRepayResultRequest {
    //渠道还款流水号
    @NotBlank(message = "渠道还款流水号不能为空")
    private String outBatchRepaymentSeq;

    //资金方还款流水号
    private String setlSeq;

}
