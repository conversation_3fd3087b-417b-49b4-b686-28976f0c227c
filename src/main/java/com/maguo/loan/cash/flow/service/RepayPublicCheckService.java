package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectElementsExtDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OfflineRepayApply;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entrance.common.constant.LvxinSysTimeMockService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 还款公共校验服务
 * 包含还款时间段，还款黑暗期和年结期间、线下还款跨日、是否逾期等校验
 */
@Service
public class RepayPublicCheckService {

    private static final Logger logger = LoggerFactory.getLogger(RepayPublicCheckService.class);
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    LvxinSysTimeMockService lvxinSysTimeMockService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    /**
     * 还款时间段，还款黑暗期和年结期间校验
     * @param loan 放款记录信息
     */
    public void repayDateCheck(Loan loan) {
        //是否放款日当天还款检验
        logger.info("是否放款日当天还款检验，放款成功时间为：" + loan.getLoanTime().toLocalDate() + "，当前日期为：" + LocalDate.now());
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new BizException(ResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE);
        }
        String projectCode = loan.getProjectCode();//项目唯一编码
        LocalTime now = LocalTime.now();//当前时间
        if(StringUtils.isBlank(projectCode)){
            logger.error("还款试算检验还款黑暗期和年结期间时，项目唯一编码为空。请检查！");
            throw new BizException(ResultCode.REPAY_PROJECT_CODE_ISNULL_ERROR);
        }
        //通过项目唯一编码查询对应的产品要素记录
        ProjectElementsDto elements = projectInfoService.queryProjectInfo(projectCode).getElements();
        if(Objects.isNull(elements)){
            logger.error("还款试算检验还款黑暗期和年结期间时，通过项目唯一编码：[" + projectCode + "]查询产品要素记录为空。请检查！");
            throw new BizException(ResultCode.REPAY_ELEMENTS_ISNULL_ERROR);
        }
        String capitalRoute = elements.getCapitalRoute().getCode();//资方路由
        //判断资方路由是直连还是路由
        if(Objects.equals(capitalRoute, CapitalRoute.DIRECT.getCode())){//为直连，获取产品要素检验黑暗期和年结期间
            //获取产品要素的还款黑暗期判断当前时间是否为黑暗期
            String repayDarkHours = elements.getRepayDarkHours();
            //为空，就默认为不限制
            if(StringUtils.isNotBlank(repayDarkHours)){
                String[] timeParts = repayDarkHours.split("-");//分割
                LocalTime startTime = LocalTime.parse(timeParts[0], DateTimeFormatter.ofPattern("HH:mm"));//黑暗期开始时间
                LocalTime endTime = LocalTime.parse(timeParts[1], DateTimeFormatter.ofPattern("HH:mm"));//黑暗期结束时间
                logger.info("为直连校验还款黑暗期，当前时间为：" + now + "，黑暗期开始时间为：" + startTime + "，黑暗期结束时间为：" + endTime);
                boolean isInDarkPeriod;//是否跨天
                if (startTime.isBefore(endTime)) {
                    //非跨天：当前时间在 [start, end) 之间
                    logger.info("非跨天：当前时间在 [" + startTime + ", " + endTime + ") 之间");
                    isInDarkPeriod = now.isAfter(startTime) && now.isBefore(endTime);
                } else {
                    //跨天：当前时间在 [start, 24:00) 或 [00:00, end) 之间
                    logger.info("跨天：当前时间在 [" + startTime + ", 24:00）或 [00:00," + endTime + ") 之间");
                    isInDarkPeriod = now.isAfter(startTime) || now.isBefore(endTime);
                }
                if (isInDarkPeriod) {
                    //为true,说明当前时间在产品要素配置的还款黑暗期时间段内,提示：当前时间为还款黑暗期，不允许还款
                    logger.info("当前时间为还款黑暗期，不允许还款");
                    throw new BizException(ResultCode.REPAY_DARK_PERIOD_ERROR);
                }
            }
            String graceNext = elements.getGraceNext().getCode();//年结是否顺延
            logger.info("年结是否顺延的值为：" + graceNext + "，为是,执行年结期间检验逻辑。为否不走。");
            //判断年结是否顺延是否为是,为是,执行年结期间检验逻辑。为否不走
            if(Objects.equals(graceNext, ActiveInactive.Y.getCode())){
                LocalDateTime dateTime = LocalDateTime.now();//获取当前日期时间
                //获取临时配置有效期起和临时配置有效期止判断当前日期时间是否为年结期间时间段内
                LocalDateTime tempStartTime = elements.getTempStartTime();//临时配置有效期起
                LocalDateTime tempEndTime = elements.getTempEndTime();//临时配置有效期止
                if(Objects.isNull(tempStartTime) || Objects.isNull(tempEndTime)){
                    logger.error("年结是否顺延为是时，项目唯一编码：[" + projectCode + "]产品要素配置的临时配置有效期起和临时配置有效期止不能为空，请检查！");
                    throw new BizException(ResultCode.REPAY_TEMP_TIME_ISNULL_ERROR);
                }
                logger.info("为直连校验年结期间，当前日期时间为：" + dateTime + "，年结开始时间为：" + tempStartTime + "，年结结束时间为：" + tempEndTime);
                //判断当前日期时间是否为年结期间时间段内
                if(dateTime.isAfter(tempStartTime) && dateTime.isBefore(tempEndTime)){
                    //为true,说明当前日期时间在配置的年结期间时间段内,提示：当前日期时间为年结期间，不允许还款
                    throw new BizException(ResultCode.REPAY_YEAR_PERIOD_ERROR);
                }
            }
        }else{//为路由，延用capitalConfig的黑暗期检验
            CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannelAndGuaranteeCompany(loan.getBankChannel(), loan.getGuaranteeCompany())
                .orElseThrow(() -> new BizException(ResultCode.CAPITAL_CONFIG_FAIL));
            if (capitalConfig.getRepayTimeStatus() == AbleStatus.DISABLE) {
                return;
            }
            LocalTime repayStartTime = DateUtil.parse(capitalConfig.getRepayStartTime());
            LocalTime repayEndTime = DateUtil.parse(capitalConfig.getRepayEndTime());
            logger.info("为路由校验还款黑暗期，当前时间为：" + now + "，还款开始时间为：" + repayStartTime + "，还款结束时间为：" + repayEndTime);
            if (repayStartTime.isAfter(now) || repayEndTime.isBefore(now)) {
                logger.error("当前时间不能还款:{}", capitalConfig.getRepayStartTime() + "|" + capitalConfig.getRepayEndTime());
                throw new BizException(ResultCode.REPAY_TIME_CHECK_FAIL);
            }
        }
    }

    /**
     * 线下还款跨日检验
     * @param date 实际转账日期
     * @param projectCode 项目唯一编码
     * @param repayApply 线下还款申请记录
     * @return 是否跨日标志
     */
    boolean checkOfflineRepayCrossDay(String date, String projectCode, OfflineRepayApply repayApply) {
        boolean isCrossDayFlag = false;//是否跨日标志
        if(StringUtils.isBlank(projectCode)){
            logger.error("线下还款跨日检验时，项目唯一编码为空。请检查！");
            throw new BizException(ResultCode.REPAY_PROJECT_CODE_ISNULL_ERROR);
        }
        //通过项目唯一编码查询对应的产品要素扩展记录
        ProjectElementsExtDto elementsExt = projectInfoService.queryProjectInfo(projectCode).getElementsExt();
        if(Objects.isNull(elementsExt)){
            logger.error("线下还款跨日检验时，通过项目唯一编码：[" + projectCode + "]查询产品要素扩展记录为空。请检查！");
            throw new BizException(ResultCode.REPAY_ELEMENTS_EXT_ISNULL_ERROR);
        }
        String allowCrossDayRepay = elementsExt.getAllowCrossDayRepay().getCode();//是否支持线下跨日还款
        logger.info("是否支持线下跨日还款的值为：" + allowCrossDayRepay + "，为否,执行跨日还款检验逻辑。为是不走。");
        //判断是否支持线下跨日还款。
        if (Objects.equals(allowCrossDayRepay, ActiveInactive.Y.getCode())) {
            return false;
        }
        LocalDateTime repayDate = null;
        if (Objects.equals(allowCrossDayRepay, ActiveInactive.N.getCode())) {//为否,执行跨日还款检验逻辑。
            logger.info("=====产品要素的是否支持线下跨日还款为否,执行跨日还款检验逻辑。========");
            // 先通过长度初步判断
            if (date.length() == 14) {
                logger.info("实际转账时间长度为14，为年月日时分秒格式");
                repayDate = LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            } else if (date.length() == 8) {
                logger.info("实际转账时间长度为8，为年月日格式");
                LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMdd"));
                repayDate = localDate.atStartOfDay();//转换为当天0点的LocalDateTime
            }
        }
        if(Objects.isNull(repayDate)){
            logger.info("实际转账时间为空，说明长度不为14也不为8，既不是年月日时分秒格式也不是年月日格式。请检查！");
            throw new BizException(ResultCode.REPAY_DATE_ISNULL_ERROR);
        }
        LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
        if(repayDate.toLocalDate().isBefore(now.toLocalDate())){
            logger.info("产品要素不支持线下跨日还款时，为跨日还款。实际转账日期为：" + repayDate.toLocalDate() + "，账单日为：" + now.toLocalDate());
            repayApply.setApplyState(ProcessState.PROCESSING);//处理中
            repayApply.setRemark("产品要素不支持线下跨日还款时，为跨日还款。实际转账日期为：" + repayDate.toLocalDate() + "，账单日为：" + now.toLocalDate() + "。请介入人工处理！");
            isCrossDayFlag = true;
        }
        return isCrossDayFlag;
    }

    /**
     * 判断是否逾期
     * @param loan 放款记录信息
     * @param period 期数
     * @param transferDate 实际转账日期
     * @return 是否逾期判断结果
     */
    public String checkIsOverdue(Loan loan, Integer period, LocalDate transferDate) {
        String isOverdue = "N";//是否逾期
        long intervalDays = 0;//间隔天数
        String projectCode = loan.getProjectCode();//项目唯一编码
        //首先检验资金渠道是否为湖消，如果不是，传默认值false，就不走后续检验逻辑。
        if(Objects.equals(loan.getBankChannel(), BankChannel.HXBK)){
            if(StringUtils.isBlank(projectCode)){
                logger.error("判断是否逾期时，项目唯一编码为空。请检查！");
                throw new BizException(ResultCode.REPAY_PROJECT_CODE_ISNULL_ERROR);
            }
            //通过项目唯一编码查询项目完整信息（带缓存）
            ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(projectCode);
            ProjectElementsDto elements = projectInfoVO.getElements();//产品要素记录
            ProjectElementsExtDto elementsExt = projectInfoVO.getElementsExt();//产品要素扩展记录
            if(Objects.isNull(elements)){
                logger.error("判断是否逾期时，通过项目唯一编码：[" + projectCode + "]查询产品要素记录为空。请检查！");
                throw new BizException(ResultCode.REPAY_ELEMENTS_ISNULL_ERROR);
            }
            if(Objects.isNull(elementsExt)){
                logger.error("判断是否逾期时，通过项目唯一编码：[" + projectCode + "]查询产品要素扩展记录为空。请检查！");
                throw new BizException(ResultCode.REPAY_ELEMENTS_EXT_ISNULL_ERROR);
            }
            //获取产品要素扩展信息的逾期宽限期天数（gracePeriodDays），然后判空。
            String gracePeriodDays = elementsExt.getGracePeriodDays();//逾期宽限期(天)
            if(StringUtils.isBlank(gracePeriodDays)){
                logger.error("判断是否逾期时，通过项目唯一编码：[" + projectCode + "]产品要素扩展记录配置的逾期宽限期(天)为空。请检查！");
                throw new BizException(ResultCode.REPAY_GRACE_PERIOD_DAYS_ISNULL_ERROR);
            }
            //获取产品要素信息的年结是否顺延，判断年结是否顺延是否为是,为是,计算临时配置有效期起和临时配置有效期止两个日期的间隔天数。为否则不计算，默认0
            String graceNext = elements.getGraceNext().getCode();//年结是否顺延
            logger.info("年结是否顺延的值为：" + graceNext + "，为是,执行年结期间检验逻辑。为否不走。");
            //判断年结是否顺延是否为是,为是,计算临时配置有效期起和临时配置有效期止两个日期的间隔天数。为否则不计算，默认0
            if(Objects.equals(graceNext, ActiveInactive.Y.getCode())){
                LocalDateTime dateTime = LocalDateTime.now();//获取当前日期时间
                //获取临时配置有效期起和临时配置有效期止计算间隔天数
                LocalDateTime tempStartTime = elements.getTempStartTime();//临时配置有效期起
                LocalDateTime tempEndTime = elements.getTempEndTime();//临时配置有效期止
                if(Objects.isNull(tempStartTime) || Objects.isNull(tempEndTime)){
                    logger.error("判断是否逾期。年结是否顺延为是时，项目唯一编码：[" + projectCode + "]产品要素配置的临时配置有效期起和临时配置有效期止不能为空，请检查！");
                    throw new BizException(ResultCode.REPAY_TEMP_TIME_ISNULL_ERROR);
                }
                logger.info("判断是否逾期，年结期间时间段检验。当前日期时间为：" + dateTime + "，年结开始时间为：" + tempStartTime + "，年结结束时间为：" + tempEndTime);
                //判断当前日期时间是否为年结期间时间段内
                if(dateTime.isAfter(tempStartTime) && dateTime.isBefore(tempEndTime)){
                    logger.info("判断是否逾期，为年结期间。计算间隔天数");
                    LocalDate startTime = LocalDate.parse(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(tempStartTime));
                    LocalDate endTime = LocalDate.parse(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(tempEndTime));
                    intervalDays = DateUtil.dateDiff(startTime, endTime) + 1;//间隔天数（已确认规则：默认加1天）
                    logger.info("临时配置有效期起为：" + startTime + "，临时配置有效期止为：" + endTime + "，间隔天数为：" + intervalDays);
                }
            }
            //宽限期天数计算：逾期宽限期天数 + 间隔天数
            long gracePeriod = Long.parseLong(gracePeriodDays) + intervalDays;
            logger.info("宽限期天数为：" + gracePeriod + "，逾期宽限期天数为：" + Long.parseLong(gracePeriodDays) + "，间隔天数为：" + intervalDays);
            //通过放款主键和期数获取还款计划记录
            RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), period);
            LocalDate planRepayDate = repayPlan.getPlanRepayDate();//计划还款日
            logger.info("获取还款计划：放款ID={}，期数={}，实际转账日期={}", loan.getId(), period, transferDate);
            LocalDateTime repayDate = Objects.isNull(transferDate) ? LocalDateTime.now() : transferDate.atStartOfDay();
            //优先获取apollo配置的资金账单日
            LocalDate actualRepayDate = LocalDate.parse(DateTimeFormatter.ofPattern("yyyy-MM-dd")
                .format(lvxinSysTimeMockService.isMockTime(repayDate)));//实际还款日期
            logger.info("计划还款日为：" + planRepayDate + "，资金账单日为：" + actualRepayDate);
            //获取还款计划应还时间和实际还款日期的差值(逾期天数)
            long overDay = DateUtil.dateDiff(planRepayDate, actualRepayDate);
            logger.info("逾期天数为：" + overDay + "，宽限期天数为：" + gracePeriod);
            //比较逾期天数是否大于宽限期天数，为true，传逾期
            if(overDay > gracePeriod){
                isOverdue = "Y";
            }
            logger.info("=====湖消是否逾期判断结果为：[" + isOverdue + "]。是否逾期字段传Y，走逾期还款。传N，走正常还款。" +
                "长银或其他不获取是否逾期字段做逻辑判断，后续有资金渠道需要可在此兼容！=====");
        }
        return isOverdue;
    }

}
