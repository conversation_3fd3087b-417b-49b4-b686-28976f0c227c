package com.maguo.loan.cash.flow.entrance.ppd.controller;

import com.maguo.loan.cash.flow.entrance.ppd.dto.ApiResult;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.LoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.RepayPlanQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.maguo.loan.cash.flow.entrance.ppd.controller.PpdApiValidator.validate;

@RestController
@RequestMapping("/ppd/api")
public class PpdLoanController {

    @Autowired
    private PpdService ppdService;

    /**
     * 放款申请
     *
     * @param request
     * @return
     */
    @PostMapping("/fundApply")
    public ApiResult loanApply(@RequestBody @Valid LoanApplyRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);

        // 业务逻辑
        return ppdService.loanApply(request);
    }



    @RequestMapping("/fundQuery")
    public ApiResult loanQuery(@RequestBody @Valid LoanQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);

        return ppdService.queryLoanResult(request);
    }

    @RequestMapping("/repayPlanSync")
    public ApiResult repayPlanQuery(@RequestBody @Valid RepayPlanQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);

        // 业务逻辑
        return ppdService.queryRepayPlan(request);
    }

    @RequestMapping("/fundCancelApply")
    public ApiResult fundCancelApply(@RequestBody @Valid RepayPlanQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);

        //00-撤销成功
        //01-拒绝撤销
        //99-处理中
        //理论上放款结果查询返回放款失败的交易撤销是一定返回成功的，返回撤销失败、处理中信也会挂单，需要人工和合作方确认
        // 业务逻辑
        // return ppdService.repayPlanQuery(request);
        return ppdService.fundCancelApply(request);
    }


    @RequestMapping("/fundCancelQuery")
    public ApiResult fundCancelQuery(@RequestBody @Valid RepayPlanQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);
        // 00-撤销成功
        //01-拒绝撤销
        //99-处理中
        //理论上放款结果查询返回放款失败的交易撤销是一定返回成功的，返回撤销失败、处理中信也会挂单，需要人工和合作方确认
        //失败原因错误码，失败时必传
        //撤销结果描述
        // 业务逻辑
        // return ppdService.repayPlanQuery(request);
        return ppdService.fundCancelQuery(request);
    }


}
