package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.*;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongRepayStatus;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.convert.PpdConvert;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.*;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.BaseRepayRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.RepayContext;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdRepayStatus;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;
import com.maguo.loan.cash.flow.enums.*;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.repository.*;
import com.maguo.loan.cash.flow.repository.bairong.BairongRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.bound.PlatformCardService;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 百融还款业务服务类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BairongRepayService {
    private static final Logger logger = LoggerFactory.getLogger(BairongRepayService.class);

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    @Autowired
    private LockService lockService;
    @Autowired
    private RepayService repayService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CapitalCardService capitalCardService;
    @Autowired
    private PlatformCardService platformCardService;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private PpdRebindRecordRepository ppdRebindRecordRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private BairongRepayApplyRecordRepository bairongRepayApplyRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private TrialService trialService;
    @Autowired
    private OfflineRepayReduceRepository offlineRepayReduceRepository;
    private static final String UNSUPPORTED_REPAYMENT_MODE = "6";

    /**
     * 还款试算
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongRepayTrialResponse trail(BairongRepayTrialRequest request) {
        logger.info("百融还款试算请求参数:{}", JsonUtil.toJsonString(request));
        Loan loan = loanRepository.findById(request.getLoanNo()).orElseThrow(() -> new BairongException(BairongResultCode.LOAN_NOT_EXIST));
        String loanId = loan.getId();
        //还款时间段校验
        repayTrailCheck(loan);
        String repaymentMode = request.getRepaymentMode();//还款类型
        //检验还款类型为6时，提示：不支持该还款类型
        if(Objects.equals(repaymentMode,UNSUPPORTED_REPAYMENT_MODE)){
            logger.info("检验还款类型为6时，提示：不支持该还款类型");
            throw new BairongException(BairongResultCode.REPAYMENT_TYPE_UNSUPPORTED);
        }
        //还款类型转换，0,1为当期，2为结清
        RepayPurpose repayPurpose = convertToRepayPurpose(repaymentMode);
        logger.info("还款类型转换，0,1为当期，2为结清。还款类型为：" + repaymentMode + "，转换后为：" + repayPurpose);
        Integer repayPeriod = Integer.parseInt(request.getPeriod());
        //如果是结清,传入最小未还的期次
        if (Objects.equals(repayPurpose,RepayPurpose.CLEAR)) {
            //查询这笔借据下还款计划是否还有待还的记录，没有就报错。提示：该笔借据下的还款计划没有待还的记录
            if (CollectionUtils.isEmpty(repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL))) {
                logger.info("该笔借据：[" + loanId + "]下的还款计划没有待还的记录");
                throw new BairongException(BairongResultCode.REPAY_PLAN_NO_NORMAL_ERROR);
            }
        }
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            loanId, repayPeriod, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("百融还款试算失败，本期已还款成功,loanId:{},periods:{}", loanId, repayPeriod);
            throw new BairongException(BairongResultCode.REPAY_PLAN_NO_NORMAL_ERROR);
        }
        try {
            TrialResultVo resultVo = trialService.repayTrial(loanId, repayPurpose, repayPeriod, request.getOperateTime());
            BairongRepayTrialResponse result = BairongConvert.INSTANCE.toRepayTrailRes(resultVo);
            result.setLoanSeq(request.getLoanSeq());//资金方放款流水号
            result.setLoanNo(request.getLoanNo());//资金方借据号
            result.setOdCommOdInt(null);//应还复利
            result.setOdOtherAmt(null);//应还其它项
            result.setOnceLimitAmt(null);//单笔限额
            result.setDayLimitAmt(null);//日限额
            logger.info("百融还款试算返回:{}", JsonUtil.toJsonString(result));
            return result;
        } catch (Exception e) {
            logger.error("百融还款试算异常", e);
            throw new BairongException(e.getMessage(), BairongResultCode.FAILURE.getMsg());
        }
    }

    /**
     * 还款申请
     * @param request 请求参数
     * @return 响应参数
     */
    public RepayApplyRespDTO processRepay(BaseRepayRequest request) {
        // 参数转换
        RepayContext context = convertToContext(request);
        // 还款
        return repay(context);
    }

    public RepayApplyRespDTO repay(RepayContext request) {
        RepayApplyRespDTO response = new RepayApplyRespDTO();

        String outerLoanId = request.getLoanReqNo();
        String repaymentGid = request.getRepayNo();
        //检查重复提交
        boolean existsRecord = bairongRepayApplyRecordRepository.existsByOutRepayId(repaymentGid);
        if (existsRecord) {
            logger.error("百融还款申请，重复提交，outerLoanId:{},outRepayId:{}", outerLoanId, repaymentGid);
            CustomRepayRecord repayRecord = customRepayRecordRepository.findByOuterRepayNo(repaymentGid);
            if (Objects.isNull(repayRecord)) {
                response.setStatus(PpdRepayStatus.FAIL.getCode());
                response.setMsg(PpdRepayStatus.FAIL.getDesc());
            } else {
                response.setStatus(PpdRepayStatus.SUCCESS.getCode());
                response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
            }
            return response;
        }
        Loan loan = loanRepository.findByOuterLoanId(outerLoanId);
        if (loan == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        String loanId = loan.getId();
        // 判断是否提前结清
        List<Integer> repayList = new ArrayList<>(Collections.singletonList(request.getRepayTerm()));
        RepayPurpose repayPurpose = RepayPurpose.toPpdRepayType(request.getRepayType());
        int period = getMinPeriod(repayList, repayPurpose);
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("百融还款申请失败，本期已还款成功,loanId:{},periods:{}", loanId, repayList);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }

        response.setStatus(PpdRepayStatus.SUCCESS.getCode());
        response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        //保存还款记录
        BairongRepayApplyRecord repayApplyRecord = bairongRepayApplyRecordRepository.findByOutRepayId(request.getRepayNo()).orElse(null);
        if (repayApplyRecord == null) {
            //repayApplyRecord = PpdConvert.INSTANCE.toRepayApplyRecord(request);
            repayApplyRecord.setLoanId(loan.getId());
            repayApplyRecord.setNeedSmsCode(WhetherState.N);
            repayApplyRecord.setRepayType(request.getRepayType());
            bairongRepayApplyRecordRepository.save(repayApplyRecord);
        }
        Order order = orderRepository.findOrderById(loan.getOrderId());
        //查询还款卡信息
        UserBankCard bankCardList = userBankCardRepository.findById(loan.getRepayCardId()).orElseThrow();
        String channelRepayId = request.getChannelRepayId();
        //如果是没有更换银行卡，就直接发起还款
        if (StringUtil.isBlank(channelRepayId)
            || (StringUtil.isNotBlank(channelRepayId) && channelRepayId.equals(bankCardList.getAgreeNo()))) {
            // 没有贷后换绑卡, 直接发起还款
            ppdRepay(request);
            return response;
        }

        //存在贷后换绑卡
        String lockKey = FlowChannel.PPCJDL.name() + "_bind_apply_" + request.getBankAcct();
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("百融还款申请，重复提交，orderNo:{}", order.getId());
                throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
            }

            return exchangeBindApply(request, loan, order.getOuterOrderId());
        } catch (Exception e) {
            logger.error("百融还款异常: 存在换绑,资方绑卡申请处理失败", e);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    private void ppdRepay(RepayContext request) {
        Loan loan = loanRepository.findByOuterLoanId(request.getLoanReqNo());
        if (loan == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        String loanId = loan.getId();
        // 判断是否提前结清
        RepayPurpose repayPurpose = RepayPurpose.toPpdRepayType(request.getRepayType());
        if (repayPurpose == null) {
            throw new PpdBizException("不支持的还款类型");
        }
        //当期还款只传1期
        int repayPeriod = request.getRepayTerm();

        //线上线下逻辑
        if (RepayMode.ONLINE.name().equals(request.getRepayMode())) {
            OnlineRepayApplyRequest repayApplyRequest = PpdConvert.INSTANCE.toOnlineApplyRequest(request, loanId, repayPeriod, repayPurpose);
            repayApplyRequest.setOtuRepayNo(request.getRepayNo());
            repayService.online(repayApplyRequest);
            logger.info("百融 还款响应,loan:{}", loanId);
        } else {
            RepayApplyDto repayApplyReq = new RepayApplyDto();
            repayApplyReq.setAmount(request.getRepayAmount());
            repayApplyReq.setOrderId(loan.getOrderId());
            repayApplyReq.setRepayPurpose(com.jinghang.ppd.api.enums.RepayPurpose.valueOf(repayPurpose.toString()));
            repayApplyReq.setPeriod(repayPeriod);
            OfflineRepayApplyRequest offlineRepayApply = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
            offlineRepayApply.setWriteOffType(WriteOffTypeEnum.DIRECT);
            offlineRepayApply.setOuterRepayNo(request.getRepayNo());
            offlineRepayApply.setRepayDate(request.getRepayTime());
            offlineRepayApply.setReductNo(request.getApplyReductNo());
            repayService.offline(offlineRepayApply);
        }
    }

    private RepayApplyRespDTO exchangeBindApply(RepayContext request, Loan loan, String orderNo) {
        RepayApplyRespDTO response = new RepayApplyRespDTO();
        response.setStatus(PpdRepayStatus.SUCCESS.getCode());
        response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        //先将之前处理中的置为失败
        PpdRebindRecord rebindRecordCapital = ppdRebindRecordRepository.
            findByCreditIdAndStateAndBoundSide(orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
        if (rebindRecordCapital != null) {
            rebindRecordCapital.setState(ProcessState.FAILED);
            ppdRebindRecordRepository.save(rebindRecordCapital);
        }

        BindCardRecord capitalBindResult = capitalBindApply(loan, orderNo, request);
        //已签约,直接发起还款
        if (ProcessState.SUCCEED == capitalBindResult.getState()) {
            //绑卡状态变更
            PpdRebindRecord rebindRecord = ppdRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
            Loan loanCard = loanRepository.findById(loan.getId()).orElseThrow();
            rebindRecord.setState(ProcessState.SUCCEED);
            rebindRecord.setBindCardRecordId(loanCard.getRepayCardId());
            ppdRebindRecordRepository.save(rebindRecord);
            //还款
            ppdRepay(request);
            return response;
        }
        logger.error("百融还款异常: 存在换绑,资方绑卡申请返回失败,reason:{}", capitalBindResult.getFailReason());
        throw new PpdBizException("还款失败");
    }

    private BindCardRecord capitalBindApply(Loan loan, String orderNo, RepayContext request) {
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(request.getBankMobile());
        exchangeCardApplyReq.setCardNo(request.getBankAcct());
        exchangeCardApplyReq.setBoundSide(BoundSide.CAPITAL);
        exchangeCardApplyReq.setCardName(request.getAcctName());
        exchangeCardApplyReq.setAgreeNo(request.getChannelRepayId());
        exchangeCardApplyReq.setIdNo(userBankCard.getCertNo());
        CardBin cardBin = platformCardService.queryCardBin(request.getBankAcct());
        if (cardBin == null) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }
        String bankAbbr = cardBin.getBankAbbr();
        exchangeCardApplyReq.setBankCode(bankAbbr);
        exchangeCardApplyReq.setBankName(cardBin.getShortName());
        //资方换绑卡
        BindCardRecord bindCardRecord = capitalCardService.bindExchangeApply(loan, exchangeCardApplyReq);
        PpdRebindRecord rebindRecord = buildPpdRebindRecord(loan, bindCardRecord, orderNo);
        ppdRebindRecordRepository.save(rebindRecord);
        return bindCardRecord;
    }

    private static PpdRebindRecord buildPpdRebindRecord(Loan byOuterLoanId, BindCardRecord bindCardRecordCapitalRe, String orderNo) {
        PpdRebindRecord rebindRecord = new PpdRebindRecord();
        rebindRecord.setState(ProcessState.PROCESSING);
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(byOuterLoanId.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecordCapitalRe.getId());
        rebindRecord.setBoundSide(BoundSide.CAPITAL);
        return rebindRecord;
    }

    private int getMinPeriod(List<Integer> repayList, RepayPurpose repayPurpose) {
        //当期还款只传1期
        int repayPeriod = repayList.get(0);
        if (RepayPurpose.CLEAR.equals(repayPurpose)) {
            //取最早的一期
            repayList = repayList.stream().sorted().toList();
            repayPeriod = repayList.get(0);
        }
        return repayPeriod;
    }

    private RepayContext convertToContext(BaseRepayRequest request) {
        RepayContext context = new RepayContext();
        // 设置公共字段
        context.setLoanReqNo(request.getLoanReqNo());
        context.setSourceCode(request.getSourceCode());
        context.setRepayNo(request.getRepayNo());
        context.setRepayType(request.getRepayType());
        context.setRepayTime(request.getRepayTime());
        context.setRepayTerm(request.getRepayTerm());
        context.setRepayAmount(request.getRepayAmount());
        context.setRepayPrincipal(request.getRepayPrincipal());
        context.setRepayInterest(request.getRepayInterest());
        context.setRepayOverdue(request.getRepayOverdue());
        context.setRepayPoundage(request.getRepayPoundage());
        context.setRepayLateFee(request.getRepayLateFee());
        // 设置特有字段
        if (request instanceof DeductApplyReqDTO t) {
            context.setRepayMode(RepayMode.ONLINE.name());
            context.setBankCode(t.getBankCode());
            context.setBankAcct(t.getBankAcct());
            context.setAcctName(t.getAcctName());
            context.setBankMobile(t.getBankMobile());
            context.setBankChannel(t.getBankChannel());
            context.setChannelRepayId(t.getChannelRepayId());
            context.setMainMemberId(t.getMainMemberId());
            context.setPayOrderNo(t.getPayOrderNo());
        } else if (request instanceof RepayNoticeReqDTO t) {
            context.setRepayMode(RepayMode.OFFLINE.name());
            context.setApplyReductNo(t.getApplyReductNo());
            context.setReductAmount(t.getReductAmount());
            context.setReductPrincipal(t.getReductPrincipal());
            context.setReductInterest(t.getReductInterest());
            context.setReductOverdue(t.getReductOverdue());
            context.setReductPoundage(t.getReductPoundage());
        }
        return context;
    }

    private void repayTrailCheck(Loan loan) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new PpdBizException(ResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE.getMsg());
        }
    }

    private RepayPurpose convertToRepayPurpose(String repayType) {
        return switch (repayType) {
            case "0","1" -> RepayPurpose.CURRENT;
            case "2" -> RepayPurpose.CLEAR;
            default -> null;
        };
    }

    /**
     * 还款结果查询
     * @param request 请求参数
     * @return 响应参数
     */
    public BairongRepayResultResponse repayQuery(BairongRepayResultRequest request) {
        BairongRepayResultResponse result = new BairongRepayResultResponse();
        BairongRepayApplyRecord baiRongRepayApplyRecord = bairongRepayApplyRecordRepository.findByOutRepayId(request.getOutBatchRepaymentSeq()).orElseThrow();
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(baiRongRepayApplyRecord.getOutRepayId());
        if (Objects.isNull(customRepayRecord)) {
            logger.info("还款结果查询失败，通过还款流水号：" + request.getOutBatchRepaymentSeq() + "查询不到对应的对客还款记录");
            result.setBillStatus(BairongRepayStatus.ERROR.getCode());//还款状态 系统异常
            //失败原因(对客还款记录不存在)
            result.setFailReason(BairongRepayStatus.CUSTOM_REPAY_RECORD_IS_NULL_ERROR.getDesc());
            return result;
        }
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findBySourceRecordId(customRepayRecord.getId());
        if (Objects.isNull(bankRepayRecord)) {
            logger.info("还款结果查询失败，通过对客还款记录的主键id：" + customRepayRecord.getId() + "查询不到对应的对资还款记录");
            result.setBillStatus(BairongRepayStatus.ERROR.getCode());//还款状态 系统异常
            //失败原因(对资还款记录不存在)
            result.setFailReason(BairongRepayStatus.BANK_REPAY_RECORD_IS_NULL_ERROR.getDesc());
            return result;
        }
        ProcessState customRepayState = customRepayRecord.getRepayState();//对客还款状态
        ProcessState state = bankRepayRecord.getState();//对资还款状态
        logger.info("======对客还款状态为：" + customRepayState + "，对资还款状态为：" + state + "。======");
        if (ProcessState.SUCCEED == customRepayState) {//01-还款成功
            result.setBillStatus(BairongRepayStatus.SUCCESS.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.SUCCESS.getDesc());//失败原因
            result.setSetlValDt(bankRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMdd")));//还款成功日期
            result.setSetlValTime(bankRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));//还款成功时间
        } else if (ProcessState.FAILED == customRepayState) {//02-还款失败
            result.setBillStatus(BairongRepayStatus.FAIL.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.FAIL.getDesc());//失败原因
        } else if (ProcessState.PROCESSING == customRepayState) {//03-处理中
            result.setBillStatus(BairongRepayStatus.PROCESSING.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.PROCESSING.getDesc());//失败原因
        } else {
            result.setBillStatus(BairongRepayStatus.PROCESSING.getCode());//还款状态
            result.setFailReason(BairongRepayStatus.PROCESSING.getDesc());//失败原因
        }
        result.setOutBatchRepaymentSeq(request.getOutBatchRepaymentSeq());//渠道还款流水号
        result.setBatchRepaymentSeq(
            Objects.nonNull(request.getSetlSeq()) ? request.getSetlSeq() : bankRepayRecord.getId());//资金方还款流水号
        result.setLoanNo(bankRepayRecord.getLoanId());//资金方借据号
        result.setApplyRePayAmt(bankRepayRecord.getAmount());//还款金额
        result.setPrinAmt(bankRepayRecord.getPrincipal());//实还本金
        result.setIntAmt(bankRepayRecord.getInterest());//实还利息
        result.setOdIntAmt(bankRepayRecord.getPenalty());//实还罚息
        //result.setCommIntAmt();//实还复利
        result.setFeeAmt(bankRepayRecord.getConsultFee());//实还费用
        result.setGrtAmt(bankRepayRecord.getGuarantee());//实还融担费
        result.setPenlAmt(bankRepayRecord.getBreach());//实还违约金
        //result.setOtherAmt();//实还其它项
        result.setDeductAmt(customRepayRecord.getReduceAmount());//减免金额
        return result;
    }

}
