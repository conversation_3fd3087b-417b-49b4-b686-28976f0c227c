package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @ClassName BaiRongRepayResponse
 * <AUTHOR>
 * @Description 还款请求响应参数
 * @Date 2025/9/11 16:51
 * @Version v1.0
 **/
@Data
public class BairongRepayResponse {
    /**
     * 渠道还款流水号
     */
    @NotBlank(message = "渠道还款流水号不能为空")
    private String outBatchRepaymentSeq;

    /**
     * 资金方还款流水号
     */
    private String setlSeq;

    /**
     * 还款状态:01	还款成功 02	还款失败 03	清算处理中
     */
    @NotBlank(message = "还款状态不能为空")
    private String billStatus;

    /**
     * 失败原因
     */
    private String failReason;

}
