package com.maguo.loan.cash.flow.entrance.bairong.dto;

// 响应数据载体
public class BairongLoanResponseData {
    private String outLoanSeq; // 渠道放款流水号
    private String loanApplyStatus; // 申请状态 (例如: 1-处理中, 2-审核通过, 3-审核拒绝, 4-放款成功, 5-放款失败)
    private String remark; // 备注信息

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getLoanApplyStatus() {
        return loanApplyStatus;
    }

    public void setLoanApplyStatus(String loanApplyStatus) {
        this.loanApplyStatus = loanApplyStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
