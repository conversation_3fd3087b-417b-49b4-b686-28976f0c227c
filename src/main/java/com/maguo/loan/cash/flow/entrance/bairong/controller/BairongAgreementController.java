package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 百融协议相关
 * @date 2025/9/11 15:53
 */
@RestController
@RequestMapping("/image/fetch")
public class BairongAgreementController extends BairongApiValidator {
    @Autowired
    private BairongService bairongService;

    /**
     * 获取协议地址
     * @param outApplSeq 申请单号
     * @param outLoanSeq 放款单号
     * @param businessStage 业务阶段
     * @return
     */
    @RequestMapping("/CJ")
    public BairongResponseData fetchAgreementUrl(@RequestParam(required = false) String outApplSeq,
                                                 @RequestParam(required = false) String outLoanSeq,
                                                 @RequestParam(required = false) String businessStage) {
        return bairongService.fetchAgreementUrl(outApplSeq,outLoanSeq,businessStage);
    }
}
