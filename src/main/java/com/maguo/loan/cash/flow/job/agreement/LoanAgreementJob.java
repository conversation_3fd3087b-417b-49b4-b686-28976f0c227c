package com.maguo.loan.cash.flow.job.agreement;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 借款合同下载
 */
@Component
@JobHandler("loanAgreementJob")
public class LoanAgreementJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanAgreementJob.class);

    private static final String JOB_NAME = "loanAgreementJob";

    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private LvXinConfig lvXinConfig;

    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;

    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    @Autowired
    private JHReconService jhReconService;

    /**
     *   绿信签章定时任务负责调度整个任务的执行流程：参数校验 -> 数据拉取 -> 配置准备 -> 循环处理。
     * @param jobParam
     */
    @Override
    public void doJob(JobParam jobParam) {
        logger.info("{} 任务启动，参数: {}", JOB_NAME, JsonUtil.toJsonString(jobParam));

        // 校验并预处理任务参数
        validateAndProcessJobParam(jobParam);

        // 根据参数拉取需要处理的借据列表
        List<Loan> loans = fetchLoans(jobParam);
        if (CollectionUtils.isEmpty(loans)) {
            logger.info("{} 未找到符合条件的放款成功借据，任务结束。", JOB_NAME);
            return;
        }
        logger.info("{} 成功拉取 {} 条借据进行处理。", JOB_NAME, loans.size());

        // 批量准备协议配置，避免在循环中重复查询
        AgreementConfigs configs = prepareAgreementConfigs(loans);

        // 遍历并处理每一条借据
        loans.forEach(loan -> processSingleLoan(loan, configs));

        // 任务完成回调
        jhReconService.saveTaskMonitoringData(jobParam, true, null);
        logger.info("{} 任务执行完成。", JOB_NAME);
    }

    public String uploadCoreAgreementFileToSftp(Loan loan, UserFile userFile, Order order,String fileName) {
        // 1. 记录方法入参，方便追踪
        logger.info("开始上传核心协议文件至SFTP，文件名：{}，放款单号：{}，资方渠道：{}，是否包含权益：{}",
            fileName, order.getOuterOrderId(), loan.getBankChannel(), loan.getIsIncludingEquity());

        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                // 根据资方渠道区分不同的sftp账号
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    logger.info("进入长银渠道SFTP上传逻辑...");

                    //不是权益客户保持之前逻辑
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {

                        String sftpPath = lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId());
                        logger.info("长银-权益客户，SFTP路径：{}，文件名：{}", sftpPath, fileName);
                        result.set(sftpPath);
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, sftpPath);
                    } else {
                        String sftpPath = lvXinConfig.getAgreementSftpPath(order.getOuterOrderId());
                        logger.info("长银-非权益客户，SFTP路径：{}，文件名：{}", sftpPath, fileName);
                        result.set(sftpPath);
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, sftpPath);

                    }
                } else if (loan.getBankChannel() == BankChannel.HXBK) {
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                        result.set(lvXinNewSFTPConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                    } else {
                        result.set(lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    }
                }
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        // 6. 记录方法返回值
        String finalResult = result.get();
        logger.info("核心协议文件上传SFTP操作完成，最终返回路径：{}", finalResult);
        return finalResult;
    }

    public String uploadFlowAgreementFileToSftp(Loan loan, String ossKey, com.maguo.loan.cash.flow.enums.FileType fileType,
                                                Order order,String fileName) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(ossBucket, ossKey, inputStream -> {
            try {
                // 根据资方渠道区分不同的sftp账号
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    //不是权益客户保持之前逻辑
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                        result.set(lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                    } else {
                        result.set(lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    }
                } else if (loan.getBankChannel() == BankChannel.HXBK) {
                    if (IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                        result.set(lvXinNewSFTPConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getIncludingEquityAgreementSftpPath(order.getOuterOrderId()));
                    } else {
                        result.set(lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                        sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getAgreementSftpPath(order.getOuterOrderId()));
                    }
                }
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }


    /**
     * 处理前一笔场景下的协议文件复制
     * 从上一条记录复制协议文件到当前复借记录，如不存在则从资金方系统获取
     */
    public void handleReloanProtocolFiles(Loan loan) {
        if (!WhetherState.Y.equals(loan.getReloan())) {
            return;
        }

        // 1. 查询用户前一笔成功记录
        List<Loan> originalLoans = loanRepository.findByFile(loan.getUserId(), loan.getBankChannel(), ProcessState.SUCCEED, loan.getApplyTime());

        if (CollectionUtils.isEmpty(originalLoans)) {
            logger.info("未找到用户首借成功记录，userId: {}", loan.getUserId());
            return;
        }
        List<com.maguo.loan.cash.flow.enums.FileType> existingFileTypes = Arrays.asList(
            com.maguo.loan.cash.flow.enums.FileType.SYNTHESIS_AUTHORIZATION,
            com.maguo.loan.cash.flow.enums.FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER
        );

        // 3. 尝试从已有文件中复制
        List<UserFile> files = userFileRepository.findByUserIdAndLoanNoAndFileTypeInOrderByUpdatedTimeDesc(
            loan.getUserId(), loan.getId(), existingFileTypes);
        if (!CollectionUtils.isEmpty(files)&&files.size()==2) {
            logger.info("已经有对应签章文件，userId: {}", loan.getUserId());
            return;
        }
        Loan originalLoan = originalLoans.get(0);
        // 2. 定义需要处理的文件类型
        List<FileType> targetFileTypes = Arrays.asList(
            FileType.SYNTHESIS_AUTHORIZATION,
            FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER
        );


        // 3. 尝试从已有文件中复制
        List<UserFile> existingFiles = userFileRepository.findByUserIdAndLoanNoAndFileTypeInOrderByUpdatedTimeDesc(
            originalLoan.getUserId(), originalLoan.getId(), existingFileTypes);

        if (!CollectionUtils.isEmpty(existingFiles)) {
            copyExistingFiles(loan, existingFiles, targetFileTypes);
        } else {
            // 4. 从资金方系统获取文件
            downloadFilesFromFinancialSystem(loan, originalLoan, targetFileTypes);
        }
    }

    /**
     * 复制已存在的文件到当前贷款记录
     */
    private void copyExistingFiles(Loan currentLoan, List<UserFile> existingFiles, List<FileType> targetFileTypes) {
        for (UserFile file : existingFiles) {
            String fileTypesStr = targetFileTypes.stream()
                .map(FileType::name)
                .collect(Collectors.joining(","));
            if (!fileTypesStr.contains(file.getFileType().name())) {
                continue;
            }
            try {
                FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(file.getFileType());
                FileDownloadResultDto fileDto = new FileDownloadResultDto();
                fileDto.setFileName(file.getFileName());
                fileDto.setOssBucket(file.getOssBucket());
                fileDto.setOssPath(file.getOssKey());
                fileDto.setFileStatus(ProcessStatus.SUCCESS);
                saveUserFile(currentLoan, cashFileType, RestResult.success(fileDto));
                logger.info("成功复制文件到当前贷款记录，loanId: {}, fileType: {}",
                    currentLoan.getId(), file.getFileType());

            } catch (Exception e) {
                logger.error("复制文件失败，loanId: {}, fileId: {}, error: {}",
                    currentLoan.getId(), file.getId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 从资金方系统下载文件
     */
    private void downloadFilesFromFinancialSystem(Loan currentLoan, Loan originalLoan, List<FileType> targetFileTypes) {
        for (FileType fileType : targetFileTypes) {
            try {
                FileDownloadDto fileDownloadDto = new FileDownloadDto();
                fileDownloadDto.setLoanId(originalLoan.getLoanNo());
                fileDownloadDto.setLoanOrderId(Optional.ofNullable(originalLoan.getLoanRecordId())
                    .orElse(originalLoan.getId()));
                fileDownloadDto.setType(fileType);
                fileDownloadDto.setProduct(Product.ZC_CASH);
                fileDownloadDto.setBankChannel(currentLoan.getBankChannel());

                RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                logger.info("下载借款合同文件，入参: {}, 出参: {}",
                    JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));

                if (!restResult.isSuccess()) {
                    logger.error("下载借款合同文件异常，loanId: {}, fileType: {}",
                        currentLoan.getId(), fileType);
                    continue;
                }

                FileDownloadResultDto resultData = restResult.getData();
                if (Objects.isNull(resultData) ||
                    StringUtils.isAnyBlank(resultData.getOssBucket(), resultData.getOssPath())) {
                    logger.info("合同文件不存在，loanId: {}, fileType: {}",
                        currentLoan.getId(), fileType);
                    continue;
                }
                saveUserFile(currentLoan, fileType, restResult);
                logger.info("成功从资金方系统下载文件，loanId: {}, fileType: {}", currentLoan.getId(), fileType);

            } catch (Exception e) {
                logger.error("从资金方系统下载文件失败，loanId: {}, fileType: {}, error: {}",
                    currentLoan.getId(), fileType, e.getMessage(), e);
            }
        }
    }

    private void saveUserFile(Loan loan, FileType fileType, RestResult<FileDownloadResultDto> restResult) {
        FileDownloadResultDto resultData = restResult.getData();
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        if(fileName.contains("资金-")){
            userFile.setFileName(fileName);
        }else {
            userFile.setFileName("资金-" + fileName);
        }
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                 PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }


    /**
     * 根据借据、订单和文件类型，解析出最终要上传的SFTP文件名。
     * @param loan 借据信息
     * @param fileType 文件类型
     * @param isCapitalOwner 文件是否属于资金方
     */
    public String resolveSftpFileName(Loan loan, String fileType, boolean isCapitalOwner) {
        String projectCode = loan.getProjectCode();
        try {

            ProjectAgreementDto config = projectAgreementFeign.getByStageAndType(projectCode, "", "", fileType);

            if (config == null) {
                logger.warn("未找到文件名配置, projectCode:{}, fileType:{}", projectCode, fileType);
                return null;
            }


            // 根据文件归属方，获取对应的文件名模板
            String fileNameTemplate = isCapitalOwner ? config.getCapitalContractName() : config.getFlowContractName();

            if (StringUtils.isBlank(fileNameTemplate)) {
                logger.warn("文件名模板为空, config:{}", JsonUtil.toJsonString(config));
                return null;
            }

            return fileNameTemplate;

        } catch (Exception e) {
            logger.error("解析SFTP文件名失败, projectCode:{}, fileType:{}", projectCode, fileType, e);
            return null;
        }
    }

    /**
     * 校验并设置Job参数的默认值
     *
     * @param jobParam 任务参数
     * @throws IllegalArgumentException 如果参数不合法
     */
    private void validateAndProcessJobParam(JobParam jobParam) {
        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if (Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        jobParam.setTaskHandler("loanAgreementJob");
        jobParam.setTaskDescription("协议文件上传-绿信");
        jobParam.setStartDate(fileDate);
        jobParam.setStartDate(fileDate);//开始时间
        jobParam.setEndDate(endDate);//结束时间
    }


    /**
     * 根据Job参数从数据库获取需要处理的借据列表
     *
     * @param jobParam 任务参数
     * @return 借据列表
     */
    private List<Loan> fetchLoans(JobParam jobParam) {
        // 优先根据指定的 loanIds 查询
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("{} 根据 loanId列表 查询，数量: {}", JOB_NAME, jobParam.getLoanIds().size());
            return loanRepository.findAllById(jobParam.getLoanIds());
        }

        LocalDateTime startDateTime = jobParam.getStartDate().atStartOfDay();
        LocalDateTime endDateTime = jobParam.getEndDate().atTime(LocalTime.MAX);

        // 根据资方和时间范围查询
        if (jobParam.getBankChannel() != null) {
            logger.info("{} 根据 资方渠道[{}] 和 时间范围[{} - {}] 查询",
                JOB_NAME, jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, jobParam.getBankChannel(), startDateTime, endDateTime, FlowChannel.LVXIN);
        }

        // 仅根据时间范围查询
        else {
            logger.info("{} 根据 时间范围[{} - {}] 查询", JOB_NAME, jobParam.getStartDate(), jobParam.getEndDate());
            return loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, startDateTime, endDateTime, FlowChannel.LVXIN);
        }
    }


    /**
     * 准备并封装协议配置信息
     *
     * @param loans 借据列表
     * @return 封装后的协议配置对象
     */
    private AgreementConfigs prepareAgreementConfigs(List<Loan> loans) {
        Set<String> projectCodes = loans.stream().map(Loan::getProjectCode).collect(Collectors.toSet());

        // 调用外部服务，一次性获取所有相关项目的协议配置
        List<ProjectAgreementDto> allConfigs = projectAgreementFeign.getByReturnStatus(
            projectCodes.stream().toList(), ActiveInactive.Y.getCode(), "");

        return new AgreementConfigs(allConfigs);
    }


    /**
     * 核心处理单元：处理单个借据的所有协议文件相关逻辑
     *
     * @param loan    待处理的借据
     * @param configs 协议配置
     */
    private void processSingleLoan(Loan loan, AgreementConfigs configs) {
        logger.info("===> 开始处理借据 loanId: {}", loan.getId());
        try {
            Order order = orderRepository.findOrderById(loan.getOrderId());
            if (order == null) {
                logger.error("未找到借据关联的订单, loanId: {}, orderId: {}", loan.getId(), loan.getOrderId());
                return;
            }

            // 1. 从资方下载合同文件并保存到本地数据库
            processCapitalAgreements(loan, configs.getFilenameCapitalMaps());

            // 2. 上传流量方签署的协议到SFTP
            processFlowAgreements(loan, order, configs.getFilenameFlowMaps());

            // 3. 上传资方系统（核心）的协议到SFTP
            processCapitalSystemAgreements(loan, order, configs.getFilenameCapitalMaps());

            // 4. 处理复借场景的特殊逻辑
            handleReloanProtocolFiles(loan);

        } catch (Exception e) {
            logger.error("处理借据协议时发生未知异常, loanId: {}", loan.getId(), e);
        }
        logger.info("<=== 借据处理完成 loanId: {}", loan.getId());
    }

    /**
     * 1. 下载并保存资方合同文件
     */
    private void processCapitalAgreements(Loan loan, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        logger.info("步骤1: 开始下载资方合同文件, loanId: {}", loan.getId());

        // 获取该借据已经下载并保存过的文件，用于后续去重
        Map<String, String> existingOssMap = getExistingOssFiles(loan);

        // 根据协议配置，确定需要下载哪些类型的文件
        List<FileType> fileTypesToDownload = filenameCapitalMaps.keySet().stream()
            .filter(key -> key.startsWith(loan.getProjectCode()))
            .map(key -> FileType.valueOf(key.replace(loan.getProjectCode(), "")))
            .toList();

        for (FileType fileType : fileTypesToDownload) {
            try {
                // 构造下载请求
                FileDownloadDto downloadRequest = createDownloadDto(loan, fileType);

                // 发起下载
                RestResult<FileDownloadResultDto> result = finLoanFileService.download(downloadRequest);
                logger.info("下载资方合同文件 [{}], loanId: {}, 结果: {}", fileType, loan.getId(), JsonUtil.toJsonString(result));

                FileDownloadResultDto data = result.getData();
                if (!result.isSuccess() || data == null || StringUtils.isAnyBlank(data.getOssBucket(), data.getOssPath())) {
                    logger.warn("资方合同下载失败或文件不存在, loanId:{}, fileType:{}", loan.getId(), fileType);
                    continue;
                }

                // 检查文件是否已下载过，如果已存在则跳过
                if (existingOssMap.containsKey(data.getOssPath()) && existingOssMap.get(data.getOssPath()).equals(data.getOssBucket())) {
                    logger.info("文件已存在，跳过保存, loanId:{}, fileType:{}", loan.getId(), fileType);
                    continue;
                }

                // 将下载成功的文件信息保存到本地数据库
                saveUserFile(loan, fileType, result);
            } catch (Exception e) {
                logger.error("下载资方合同 [{}] 时发生异常, loanId:{}", fileType, loan.getId(), e);
            }
        }
    }


    /**
     * 3. 上传资方系统（核心）的协议到SFTP
     */
    private void processCapitalSystemAgreements(Loan loan, Order order, Map<String, ProjectAgreementDto> filenameCapitalMaps) {
        logger.info("步骤3: 开始上传资方系统协议文件, loanId: {}", loan.getId());

        // 查询已落库的资方文件（包括刚刚下载的）
        List<UserFile> userFiles = userFileRepository.findByUserIdAndLoanNo(loan.getUserId(), loan.getId());

        if (CollectionUtils.isEmpty(userFiles)) {
            logger.info("该借据无资方系统协议需要上传, loanId: {}", loan.getId());
            return;
        }

        for (UserFile userFile : userFiles) {
            // 过滤掉某些不需要上传的文件类型
            if (com.maguo.loan.cash.flow.enums.FileType.CREDIT_SETTLE_VOUCHER_FILE.equals(userFile.getFileType())) {
                continue;
            }

            try {
                // 根据配置动态获取上传到SFTP的文件名
                String configKey = loan.getProjectCode() + userFile.getFileType().name();
                ProjectAgreementDto config = filenameCapitalMaps.get(configKey);
                if (config == null || StringUtils.isBlank(config.getCapitalContractName())) {
                    logger.warn("未找到资方协议 [{}] 的文件名配置, loanId: {}", userFile.getFileType().name(), loan.getId());
                    continue;
                }
                String sftpFileName = config.getCapitalContractName();

                // 调用SFTP上传工具方法
                uploadCoreAgreementFileToSftp(loan, userFile, order, sftpFileName);
            } catch (Exception e) {
                logger.error("上传资方协议 [{}] 失败, loanId: {}, userFileId: {}", userFile.getFileType(), loan.getId(), userFile.getId(), e);
            }
        }
    }


    /**
     * 2. 上传流量方协议到SFTP
     */
    private void processFlowAgreements(Loan loan, Order order, Map<String, ProjectAgreementDto> filenameFlowMaps) {
        logger.info("步骤2: 开始上传流量方协议文件, loanId: {}", loan.getId());

        // 查询流量侧已成功签署的协议记录
        List<AgreementSignatureRecord> records = agreementSignatureRecordRepository.findByOrderNoAndSignState(order.getOuterOrderId(), ProcessState.SUCCEED.name());

        if (CollectionUtils.isEmpty(records)) {
            logger.info("该借据无流量方协议需要上传, loanId: {}", loan.getId());
            return;
        }

        for (AgreementSignatureRecord record : records) {
            try {
                // 根据配置动态获取上传到SFTP的文件名
                String configKey = loan.getProjectCode() + record.getFileType().name();
                ProjectAgreementDto config = filenameFlowMaps.get(configKey);
                if (config == null || StringUtils.isBlank(config.getFlowContractName())) {
                    logger.warn("未找到流量方协议 [{}] 的文件名配置, loanId: {}", record.getFileType().name(), loan.getId());
                    continue;
                }
                String sftpFileName = config.getFlowContractName();

                // 调用SFTP上传工具方法
                uploadFlowAgreementFileToSftp(loan, record.getCommonOssUrl(), record.getFileType(), order, sftpFileName);
            } catch (Exception e) {
                logger.error("上传流量方协议 [{}] 失败, loanId: {}, recordId: {}", record.getFileType(), loan.getId(), record.getId(), e);
            }
        }
    }

    private static class AgreementConfigs {
        private final Map<String, ProjectAgreementDto> filenameFlowMaps;
        private final Map<String, ProjectAgreementDto> filenameCapitalMaps;

        public AgreementConfigs(List<ProjectAgreementDto> allConfigs) {
            // 用于流量方协议的文件名映射
            this.filenameFlowMaps = allConfigs.stream().collect(
                Collectors.toMap(dto -> dto.getProjectCode() + dto.getContractTemplateType().name(),
                    Function.identity(), (o1, o2) -> o1)
            );

            // 过滤出属于资方的协议，并创建文件名映射
            this.filenameCapitalMaps = allConfigs.stream()
                .filter(dto -> TemplateOwner.CAPITAL.name().equals(dto.getTemplateOwner()))
                .collect(Collectors.toMap(dto -> dto.getProjectCode() + dto.getContractTemplateType().name(),
                    Function.identity(), (o1, o2) -> o1));
        }

        public Map<String, ProjectAgreementDto> getFilenameFlowMaps() {
            return filenameFlowMaps;
        }

        public Map<String, ProjectAgreementDto> getFilenameCapitalMaps() {
            return filenameCapitalMaps;
        }
    }

    /**
     * 查询指定借据已存在的OSS文件记录。
     *
     * @param loan 借据对象
     * @return 一个Map，Key为OSS路径(ossKey)，Value为OSS桶(ossBucket)
     */
    private Map<String, String> getExistingOssFiles(Loan loan) {
        // 使用 loanRecordId（如果存在）或 loanId 作为关联ID进行查询
        String relatedId = Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId());
        List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(relatedId);
        if (CollectionUtil.isEmpty(userFiles)) {
            return Collections.emptyMap();
        }
        // 将查询结果转换为Map，方便快速查找
        return userFiles.stream().collect(
            Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, (existing, replacement) -> existing) // 保留已存在的key
        );
    }

    /**
     *创建文件下载请求的数据传输对象(DTO)。
     *
     * @param loan     借据对象
     * @param fileType 需要下载的文件类型
     * @return 配置好的FileDownloadDto实例
     */
    private FileDownloadDto createDownloadDto(Loan loan, FileType fileType) {
        FileDownloadDto fileDownloadDto = new FileDownloadDto();
        fileDownloadDto.setLoanId(loan.getLoanNo());
        fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        fileDownloadDto.setType(fileType);
        fileDownloadDto.setProduct(Product.ZC_CASH); // 根据业务场景设置
        fileDownloadDto.setBankChannel(loan.getBankChannel());
        return fileDownloadDto;
    }


}

