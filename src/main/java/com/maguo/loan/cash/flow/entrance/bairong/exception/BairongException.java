package com.maguo.loan.cash.flow.entrance.bairong.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 百融异常
 * @date 2025/9/10 14:51
 */
public class BairongException extends RuntimeException {

    /**
     * 签名验证失败
     */
    public static final BairongException SIGN_VERIFY_FAIL = new BairongException(BairongResultCode.SIGN_VERIFY_FAIL);
    /**
     * 解密失败
     */
    public static final BairongException DECRYPT_FAIL = new BairongException(BairongResultCode.DECRYPT_FAIL);
    /**
     * 加密失败
     */
    public static final BairongException ENCRYPT_FAIL = new BairongException(BairongResultCode.ENCRYPT_FAIL);
    /**
     * 参数错误
     */
    public static final BairongException PARAM_ERROR = new BairongException(BairongResultCode.INVALID_PARAM);
    private final String errorCode;

    public BairongException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BairongException(BairongResultCode code) {
        super(code.getMsg());
        this.errorCode = code.getCode();
    }

    public String getErrorCode() {
        return errorCode;
    }
}
