package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile.SettleFileApply;
import com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile.SettleFileApplyResult;
import com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile.SettleFileResult;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @title: 百融结清证明
 * @desc:
 * @date 2025/9/11 09:59
 */
@RestController
@RequestMapping("/settleFile")
public class BairongClearVoucherController extends BairongApiValidator {
    private static final Logger log = LoggerFactory.getLogger(BairongClearVoucherController.class);

    @Autowired
    private BairongService bairongService;

    @RequestMapping(value = "/apply", method = RequestMethod.POST)
    public SettleFileApplyResult apply(@RequestBody @Valid SettleFileApply settleFileApply, BindingResult bindingResult) {
        try {
            //参数校验
            validate(bindingResult);

            return bairongService.apply(settleFileApply.getLoanNo());

        } catch (Exception e) {
            log.error("申请开具百融结清证明失败", e);
            return SettleFileApplyResult.fail();
        }
    }
}
