package com.maguo.loan.cash.flow.entrance.ppd.controller;

import com.jinghang.ppd.api.dto.collection.PpdReduceApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.PpdReduceApplyDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.ReduceApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdRepayService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/7/23 11:22
 **/
@RestController
@RequestMapping("/collect/api")
public class PpdCollectionController extends PpdApiValidator {
    private static final Logger log = LoggerFactory.getLogger(PpdCollectionController.class);

    @Autowired
    private PpdRepayService ppdRepayService;

    /**
     * 资方催收减免申请
     */
    @RequestMapping("/reduction")
    public PpdReduceApplyResponse reduction(@RequestBody @Valid PpdReduceApplyDTO request, BindingResult bindingResult) {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return ppdRepayService.reduction(request);
    }

    /**
     * 资方催收减免申请校验
     */
    @RequestMapping("/reduction/check")
    public PpdReduceApplyResponse check(@RequestBody @Valid PpdReduceApplyDTO request, BindingResult bindingResult) {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return ppdRepayService.check(request);
    }

}
