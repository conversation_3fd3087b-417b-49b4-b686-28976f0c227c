package com.maguo.loan.cash.flow.entrance.bairong.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 加密请求报文
 * @date 2025/9/10 14:39
 */
public class BairongRequestData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道号码
     */
    private String channel;

    /**
     * 加密后的报文（AES加密后Base64编码）
     */
    private String json;

    /**
     * 接入方的系统id
     */
    private String appId;

    /**
     * 签名（首字母+唯一流水号）
     */
    private String sign;

    /**
     * 随机字符串（当前请求时间，格式
     *
     * 为yyyy-MM-dd HH:mm:ss）
     */
    private String randomStr;

    // getter and setter methods
    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRandomStr() {
        return randomStr;
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    @Override
    public String toString() {
        return "EncryptedRequestData{" +
            "channel='" + channel + '\'' +
            ", json='" + json + '\'' +
            ", appId='" + appId + '\'' +
            ", sign='" + sign + '\'' +
            ", randomStr='" + randomStr + '\'' +
            '}';
    }
}
