package com.maguo.loan.cash.flow.entrance.common.strategy.fql;

import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class FqlFlowStrategy implements ProductCodeStrategy {

    @Autowired
    private CacheService cacheService;

    public static final String MAPPING_KEY_PREFIX = "project_mappings:code:";

    public ProjectProductMappingRepository projectProductMappingRepository;

    @Override
    public boolean supports(String source, Map<String, String> params) {
        return "FLOW".equalsIgnoreCase(source) &&
            FlowChannel.FQLQY001.name().equalsIgnoreCase(params.get("flowSource"));
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String partnerCode = params.get("partnerCode");
        if (StringUtils.isEmpty(partnerCode)) {
            throw new IllegalArgumentException("分期乐流量映射必须包含 'partnerCode' 参数");
        }
        String redisKey = MAPPING_KEY_PREFIX + partnerCode;
        String projectCode = (String) cacheService.get(redisKey);

        if (StringUtils.isEmpty(projectCode)) {
            // 缓存中没有，从数据库查询
            Optional<ProjectProductMapping> mapping = projectProductMappingRepository.findByProductCode(partnerCode);
            if (!mapping.isPresent()) {
                throw new IllegalArgumentException("未找到对应的产品映射配置");
            }
            projectCode = mapping.get().getProjectCode();
            // 写入缓存，并设置过期时间
            cacheService.put(redisKey, projectCode, 24, TimeUnit.HOURS);
        }
        return projectCode;
    }
}
