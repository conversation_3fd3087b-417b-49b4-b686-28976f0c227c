package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.repay.BairongRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.service.BairongRepayService;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.DeductApplyReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayApplyRespDTO;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/9/11 11:22
 **/
@RestController
@RequestMapping("/repayment")
public class BairongRepayController extends BairongApiValidator {
    private static final Logger logger = LoggerFactory.getLogger(BairongRepayController.class);

    @Autowired
    private BairongRepayService baiRongRepayService;

    /**
     * 还款试算
     */
    @RequestMapping("/trial/BRYC")
    public BairongRepayTrialResponse repayTrial(@RequestBody @Valid BairongRepayTrialRequest request, BindingResult bindingResult) throws BairongException {
        //必填参数校验
        validate(bindingResult);
        //业务逻辑
        return baiRongRepayService.trail(request);
    }

    /**
     * 还款申请
     */
    @RequestMapping("/withholdApply")
    public RepayApplyRespDTO repayApply(@RequestBody @Valid DeductApplyReqDTO request, BindingResult bindingResult) throws BairongException {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return baiRongRepayService.processRepay(request);
    }

    /**
     * 还款结果查询
     */
    @RequestMapping("/withholdResultQuery")
    public BairongRepayResultResponse repayQuery(@RequestBody @Valid BairongRepayResultRequest request, BindingResult bindingResult) throws BairongException {
        // 必填参数校验
        validate(bindingResult);
        // 业务逻辑
        return baiRongRepayService.repayQuery(request);
    }

}
