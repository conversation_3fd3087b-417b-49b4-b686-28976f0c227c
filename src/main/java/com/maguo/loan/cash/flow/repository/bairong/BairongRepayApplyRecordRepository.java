package com.maguo.loan.cash.flow.repository.bairong;

import com.maguo.loan.cash.flow.entity.BairongRepayApplyRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/9/11 16:26
 */
public interface BairongRepayApplyRecordRepository extends JpaRepository<BairongRepayApplyRecord, String> {

    Optional<BairongRepayApplyRecord> findByOutRepayId(String outRepayId);

    boolean existsByOutRepayId(String outRepayId);

    BairongRepayApplyRecord findByOutLoanIdAndLoanIdAndOutRepayId(String loanGid, String partnerOrderNo, String repaymentGid);
}
