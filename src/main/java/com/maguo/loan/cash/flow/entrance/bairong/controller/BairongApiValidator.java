package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/11 11:48
 */
public class BairongApiValidator {

    public static void validate(BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            StringBuilder sb = new StringBuilder();
            for (FieldError fieldError : bindingResult.getFieldErrors()) {
                sb.append(fieldError.getField()).append(":").append(fieldError.getDefaultMessage()).append(";");
            }
            throw new BairongException(BairongResultCode.INVALID_PARAM.getCode(), sb.toString());
        }
    }
}
