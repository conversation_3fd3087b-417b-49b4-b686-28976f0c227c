package com.maguo.loan.cash.flow.entrance.bairong.exception;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 百融返回码枚举
 * @date 2025/9/11 9:19
 */
public enum BairongResultCode {
    SUCCESS("0000", "请求成功"),
    FAILURE("0001", "内部错误，接口调用失败"),
    INVALID_PARAM("0003", "参数错误"),
    SIGN_VERIFY_FAIL("0004", "签名错误"),
    DECRYPT_FAIL("0005", "解密错误"),
    ENCRYPT_FAIL("0006", "加密错误"),
    UNKNOWN_ERROR("9999", "未知错误"),

    INTERNAL_ERROR("0001", "内部错误，接口调用失败"),
    PARAM_ERROR("0003", "参数错误"),
    SIGNATURE_ERROR("0004", "签名错误"),
    DECRYPT_ERROR("0005", "解密错误"),
    OUT_LOAN_SEQ_CAN_NOT_BE_NULL("10011", "[outLoanSeq]不能为空"),
    ORDER_ALREADY_REJECT("10005", "订单已拒绝"),
    ALREADY_AN_ORDER_IN_TRANSIT("10004", "存在在途订单"),
    OVERALL_SCORE_IS_INSUFFICIENT("10011", "综合评分不足"),
    SYSTEM_ERROR("9999", "标识失败"),
    LOAN_NOT_EXIST("10030", "借款记录不存在"),
    REPAYMENT_TYPE_UNSUPPORTED("10151", "不支持该还款类型"),
    REPAY_PLAN_NO_NORMAL_ERROR("10152", "该笔借据下的还款计划没有待还的记录"),
    CURRENT_PERIOD_REPAYMENT_SUCCESS_ERROR("10153", "百融还款试算失败，本期已还款成功"),

    LOAN_NOT_EXIST_OR_ORDER_NOT_CLEAR("10201","借据不存在或订单未结清"),
    CLEAR_ALREADY_EXIST("10202","结清证明已存在"),
    CLEAR_APPLY_FAIL("10203","下载结清证明申请失败"),
    CLEAR_PROCESSING("10204","处理中"),


    ;


    private String code;
    private String msg;

    BairongResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BairongResultCode getByCode(String code) {
        for (BairongResultCode resultCode : BairongResultCode.values()) {
            if (Objects.equals(resultCode.getCode(), code)) {
                return resultCode;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
