package com.maguo.loan.cash.flow.entrance.bairong.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.entity.BaiRongApplyBasicInfo;
import com.maguo.loan.cash.flow.entity.BaiRongApplyRecord;
import com.maguo.loan.cash.flow.entity.BaiRongLoanApply;
import com.maguo.loan.cash.flow.entity.BairongSettlefileApplyRecord;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.BindCardRelation;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entrance.bairong.convert.BairongConvert;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.bairong.dto.loan.BairongLoanRequest;
import com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile.SettleFileApplyResult;
import com.maguo.loan.cash.flow.entrance.bairong.dto.settlefile.SettleFileResult;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongLoanStatus;
import com.maguo.loan.cash.flow.entrance.bairong.enums.BairongSettleFileStatus;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongBizException;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongResultCode;
import com.maguo.loan.cash.flow.entrance.common.covert.ProjectConfigMapperHelper;
import com.maguo.loan.cash.flow.entrance.common.service.LoanOperateService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.impl.AlipayCardBinService;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.BindCardRelationRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderBindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyBasicInfoRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongLoanApplyRepository;
import com.maguo.loan.cash.flow.repository.bairong.BairongSettlefileApplyRecordRepository;
import com.maguo.loan.cash.flow.service.CheckService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.UserFileService;
import com.maguo.loan.cash.flow.service.UserService;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonCheckService;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 百融 业务服务类
 * @Date 2025/9/10 14:40
 * @Version v1.0
 **/
@Slf4j
@Service
public class BairongService {

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    public static final long SEVEN = 7L;
    public static final Integer THIRTY = 30;

    private static final Logger logger = LoggerFactory.getLogger(BairongService.class);

    @Autowired
    private BairongLoanApplyRepository baiRongLoanApplyRepository;
    @Autowired
    private BairongApplyBasicInfoRepository baiRongApplyBasicInfoRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private OrderService orderService;
    @Autowired
    private LoanCommonCheckService loanCommonCheckService;
    @Autowired
    private LoanOperateService loanOperateService;
    @Autowired
    private AlipayCardBinService alipayCardBinService;
    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;
    @Autowired
    private BindCardRelationRepository relationRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private OrderBindCardRecordRepository orderBindCardRecordRepository;
    @Autowired
    private ProjectCodeMapper projectCodeMapper;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private CheckService checkService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectInfoService projectInfoService;
    @Autowired
    private UserInfoRepository userInfoRepository;
    @Autowired
    UserRiskRecordRepository userRiskRecordRepository;
    @Autowired
    private ProjectConfigMapperHelper projectConfigMapperHelper;
    @Autowired
    private BairongApplyRecordRepository baiRongApplyRecordRepository;
    @Autowired
    private MqService mqService;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private BairongSettlefileApplyRecordRepository bairongSettlefileApplyRecordRepository;

    public SettleFileApplyResult apply(String loanNo) {
        List<Loan> loanList = loanRepository.findClearLoanByLoanIdInAndFlowChannel(List.of(loanNo), FlowChannel.LTFQ);

        //按借据单个下载
        if (CollectionUtil.isEmpty(loanList)) {
            return SettleFileApplyResult.fail(BairongResultCode.LOAN_NOT_EXIST_OR_ORDER_NOT_CLEAR.getMsg());
        } else {
            SettleFileResult settleFileResult = userFileService.downloadByFileTypeBaiRong(loanList.get(0), FileType.CREDIT_SETTLE_VOUCHER_FILE);
            BairongSettlefileApplyRecord record = bairongSettlefileApplyRecordRepository.findByLoanId(loanNo);
            if (record == null) {
                record = new BairongSettlefileApplyRecord();
            }
            record.setLoanId(loanNo);
            record.setStatus(settleFileResult.getStatus());
            record.setFailReason(settleFileResult.getRemark());
            if (BairongSettleFileStatus.SUCCESS.getStatus().equals(settleFileResult.getStatus())) {
                record.setFileName(settleFileResult.getFileName());
                record.setFileContent(settleFileResult.getFileData());
            }
            record = bairongSettlefileApplyRecordRepository.save(record);

            return SettleFileApplyResult.build(record.getStatus(), record.getFailReason());
        }

    }


    public BairongResponse approval(BairongLoanRequest request) {
        saveInitialRequest(request);

        return handleCredit(request);
    }

    /**
     * @param request 百融的原始请求
     * @return 处理结果
     */
    private BairongResponse handleCredit(BairongLoanRequest request) {
        String orderNo = request.getOutLoanSeq();
        logger.info("百融授信流程开始, outLoanSeq: {}", orderNo);
        Map<String, String> params = new HashMap<>();
        params.put("flowSource", FlowChannel.BAIRONG.name());
        params.put("channelCode", request.getChannelCode());
        String projectCode = projectCodeMapper.getProjectCode("FLOW", params);
        request.setProjectCode(projectCode);
        BairongResponse approvalResponse = new BairongResponse();

        try {
            if (StringUtil.isBlank(orderNo)) {
                logger.error("百融授信进件 对方申请订单号为空,orderNo:{}", orderNo);
                throw new BairongBizException(BairongResultCode.OUT_LOAN_SEQ_CAN_NOT_BE_NULL);
            }
            //查询预订单，是否是可以进件的数据
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.BAIRONG).orElse(new PreOrder());

            preOrder.setProjectCode(projectCode);
            if (PreOrderState.AUDIT_REJECT == preOrder.getPreOrderState()) {
                throw new BairongBizException(BairongResultCode.ORDER_ALREADY_REJECT);
            }
            //30天 内部风控拒绝
            List<UserRiskRecord> userRiskRecords = queryThirtyDayRiskRejectRecord(projectCode, request.getIdNo(), FlowChannel.BAIRONG);
            if (!CollectionUtils.isEmpty(userRiskRecords)) {
                throw new BairongBizException(BairongResultCode.OVERALL_SCORE_IS_INSUFFICIENT);
            }

            // 30天内授信失败检验
            BankChannel bankChannel = BairongConvert.toBankChannelPub(request.getChannelCode());

            List<Order> orders = queryThirtyDayCreditFailRecord(request.getIdNo(), FlowChannel.BAIRONG, bankChannel, projectCode);

            if (!CollectionUtils.isEmpty(orders)) {
                throw new BairongBizException(BairongResultCode.OVERALL_SCORE_IS_INSUFFICIENT);
            }
            if (PreOrderState.AUDITING == preOrder.getPreOrderState()) {
                approvalResponse.setCode("0000");
                return approvalResponse;
            }
            //填充预订单
            preOrder = BairongConvert.INSTANCE.toPreOrder(preOrder, request, projectConfigMapperHelper);
            //保存预订单
            preOrder = preOrderRepository.save(preOrder);
            //保存绿信申请记录
            BaiRongApplyRecord baiRongApplyRecord = baiRongApplyRecordRepository.findByOrderNo(orderNo).orElse(null);
            if (baiRongApplyRecord == null) {
                //新建
                baiRongApplyRecord = BairongConvert.INSTANCE.toBaiRongApplyRecord(request);
            } else {
                //更新
                baiRongApplyRecord = BairongConvert.INSTANCE.toBaiRongApplyRecord(baiRongApplyRecord, request);
            }
            baiRongApplyRecordRepository.save(baiRongApplyRecord);

            //判断是否存在在途订单
            String onOrder = checkService.onOrderOrRiskByCertNo(preOrder);
            if (StringUtil.isNotBlank(onOrder)) {
                preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
                preOrder.setIsReject(WhetherState.Y);
                preOrder.setRemark(onOrder);
                preOrderRepository.save(preOrder);
                //进件失败
                throw new BairongBizException(BairongResultCode.ALREADY_AN_ORDER_IN_TRANSIT);
            }
            //注册用户
            UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
            if (Objects.isNull(userRegister)) {
                userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
            }
//            //user_info
//            UserInfo userInfo = LvxinConvert.INSTANCE.toUserInfo(preOrder, applyRecord);
//            //user_ocr
//            UserOcr userOcr = createUserOcr(preOrder, applyRecord);
//            //user_face
//            UserFace userFace = createUserFace(preOrder, applyRecord);
//            //device
//            UserDevice userDevice = createUserDevice(applyRecord);
//            //contactInfos
//            List<UserContactInfo> contactInfos = createUserContactInfos(applyRecord);
//            UserRiskRecord riskRecord = userService.register(userInfo, userOcr, userFace, userDevice, contactInfos, FlowChannel.LVXIN, preOrder.getApplyChannel(), preOrder);
//            String userId = riskRecord.getUserId();
//            //保存风控id
//            preOrder.setRiskId(riskRecord.getId());
//            preOrder.setOpenId(userId);
//            preOrder = preOrderRepository.save(preOrder);
//            //填充userId
//            userRegister.setUserId(userId);
//            userRegisterRepository.saveAndFlush(userRegister);
//            userFileService.saveIdCardFace(userId, userOcr.getHeadOssBucket(), userOcr.getHeadOssKey());
//            userFileService.saveIdCardNation(userId, userOcr.getNationOssBucket(), userOcr.getNationOssKey());
//            userFileService.saveFaceOcr(userId, userFace.getOssBucket(), userFace.getOssKey());
            if (preOrder.getBankChannel() != BankChannel.HXBK) {
                //异步同步蚂蚁接口
                mqService.submitMayiAccess(preOrder.getId());
            }
            //添加 影像件下载异常 延迟补偿
            mqService.submitImgFileDownload(baiRongApplyRecord.getId());
            //更新预订单为审核中
            preOrder.setPreOrderState(PreOrderState.AUDITING);

            preOrderRepository.saveAndFlush(preOrder);
            approvalResponse.setCode("00");
        } catch (BairongBizException e) {
            logger.error("百融授信失败,orderNo:" + orderNo, e);
            throw e;
        } catch (Exception e) {
            logger.error("百融授信失败,orderNo:" + orderNo, e);
            throw new BairongBizException(BairongResultCode.SYSTEM_ERROR);
        }
        return approvalResponse;
    }

    private List<UserRiskRecord> queryThirtyDayRiskRejectRecord(String projectCode,
                                                                String idCard, FlowChannel flowChannel) {
        ProjectInfoDto projectInfoDto = projectInfoService.queryProjectInfo(projectCode);
        int creditLockDays = projectInfoDto.getElements().getCreditLockDays();
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(creditLockDays);
        // 先查user表
        UserInfo userInfo = userInfoRepository.findByCertNo(idCard);
        if (null != userInfo) {
            //再查 risk表
            return userRiskRecordRepository.queryThirtyDayRiskRejectRecord(userInfo.getId(), failCreditDate, AuditState.REJECT, flowChannel);
        }
        return new ArrayList<>();
    }

    /**
     * 保存百融的原始请求
     */
    private void saveInitialRequest(BairongLoanRequest request) {

        BaiRongApplyBasicInfo basicInfo = BairongConvert.INSTANCE.toBaiRongApplyBasicInfo(request);
        basicInfo = baiRongApplyBasicInfoRepository.save(basicInfo);

        BaiRongLoanApply loanApply = BairongConvert.INSTANCE.toBaiRongLoanApply(request, basicInfo.getId());
        baiRongLoanApplyRepository.save(loanApply);
    }

    public List<Order> queryThirtyDayCreditFailRecord(String certNo, FlowChannel flowChannel, BankChannel bankChannel,
                                                      String projectCode) {
        ProjectInfoDto projectInfoDto = projectInfoService.queryProjectInfo(projectCode);

        int creditLockDays = projectInfoDto.getElements().getCreditLockDays();
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(creditLockDays);

        return orderRepository.queryThirtyDayCreditFailRecordLX(
            OrderState.CREDIT_FAIL, certNo, failCreditDate, flowChannel, bankChannel
        );
    }

    /**
     * 百融放款接口——(提供给授信申请成功之后调用的放款接口)
     *
     * @param request 放款申请请求参数
     * @return 返回数据
     */
    public BairongLoanApplyResponse loanApply(BairongLoanApplyRequest request) {
        BairongLoanApplyResponse response = new BairongLoanApplyResponse();
        // 渠道放款流水号
        String outLoanSeq = request.getOutLoanSeq();
        // 查询订单数据
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(outLoanSeq, FlowChannel.LTFQ);
        // 判断订单状态为AUDIT_PASS(平台风控通过)才可以继续执行放款申请，否则放款失败
        if (OrderState.AUDIT_PASS != order.getOrderState()) {
            logger.error("【新流程】授信状态为:{},无法发起借款,orderId:{},outLoanSeq:{}", order.getOrderState(), order.getId(), outLoanSeq);
            return BairongLoanApplyResponse.failure(outLoanSeq, BairongLoanStatus.LOAN_FAIL.getCode(), "无有效授信，不可发起借款");
        }
        // 【公共需求】风控通过时间校验
        if (verifyOrderThirtyDayExpire(order, outLoanSeq)) {
            return BairongLoanApplyResponse.failure(outLoanSeq, BairongLoanStatus.LOAN_FAILED.getCode(), BairongLoanStatus.LOAN_FAILED.getDesc());
        }
        // 统一校验放款阶段参数（校验还款类型、校验借款期限、校验单笔提现步长）
        Integer repaymentType = BairongConvert.toMtdCdePub(request.getMtdCde());
        try {
            loanCommonCheckService.checkLoanParameters(order, repaymentType);
        } catch (BizException e) {
            // 校验失败，更新订单状态为LOAN_FAIL
            logger.error("放款参数校验失败，更新订单状态为LOAN_FAIL, orderId: {}", order.getId(), e);
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(e.getMessage());
            orderRepository.save(order);
            return BairongLoanApplyResponse.failure(outLoanSeq, BairongLoanStatus.LOAN_FAIL.getCode(), "放款申请参数校验失败");
        }
        String checked = loanOperateService.checkAmount(order, request.getDnAmt());
        if (StringUtils.isNotEmpty(checked)) {
            //设置订单状态为放款失败
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(checked);
            orderRepository.save(order);
            response.setPayMsg(checked);
            response.setDnSts(BairongLoanStatus.LOAN_FAIL.getCode());
            response.setOutLoanSeq(outLoanSeq);
            return response;
        }
        // 直接发起放款
        BairongLoanApplyRequest.AccountInfo accountInfo = request.getAccountInfo();
        // 调用接口查询卡归属码值
        CardBin query = alipayCardBinService.query(accountInfo.getAcctNo());
        if (ObjectUtils.isEmpty(query)) {
            return BairongLoanApplyResponse.failure(outLoanSeq, BairongLoanStatus.LOAN_FAIL.getCode(), "不支持的银行卡");
        }
        BindCardRecord finalRecord = new BindCardRecord();
        finalRecord.setUserId(request.getUserId());
        finalRecord.setState(ProcessState.PROCESSING);
        finalRecord.setChannel(ProtocolChannel.BF);
        finalRecord.setCertNo(accountInfo.getIdNo());
        finalRecord.setPhone(accountInfo.getAcctPhone());
        finalRecord.setName(accountInfo.getAcctName());
        finalRecord.setBankCardNo(accountInfo.getAcctNo());
        finalRecord.setAgreeNo(accountInfo.getAgreeNum());
        finalRecord.setBankCode(query.getBankAbbr());
        finalRecord.setBankName(query.getName());
        BindCardRecord save = bindCardRecordRepository.save(finalRecord);
        BindCardRelation bindCardRelation = new BindCardRelation();
        bindCardRelation.setRelatedId(save.getId());
        bindCardRelation.setBindStage(LoanStage.LOAN);
        bindCardRelation.setBindCardApplyId(finalRecord.getId());
        bindCardRelation.setUserId(request.getUserId());
        relationRepository.save(bindCardRelation);
        // 绑卡成功, 插入用户绑卡记录
        UserBankCard userBankCard = new UserBankCard();
        userBankCard.setUserId(request.getUserId());
        userBankCard.setCardNo(accountInfo.getAcctNo());
        userBankCard.setCardName(accountInfo.getAcctName());
        userBankCard.setPhone(accountInfo.getAcctPhone());
        userBankCard.setCertNo(accountInfo.getIdNo());
        userBankCard.setChannel(ProtocolChannel.BF);
        userBankCard.setMerchantNo(FlowChannel.LTFQ.name());
        userBankCard.setAgreeNo(accountInfo.getAgreeNum());
        userBankCard.setBankCode(finalRecord.getBankCode());
        userBankCard.setBankName(finalRecord.getBankName());
        userBankCardRepository.save(userBankCard);
        // 借款用途
        order.setLoanPurpose(BairongConvert.toLoanPurpose(request.getPurpose()));
        order.setId(order.getId());
        order.setLoanCardId(userBankCard.getId());
        order.setOrderSubmitState(WhetherState.Y);
        order = orderRepository.save(order);
        // 调用放款下单接口
        orderService.apply(order.getId(), order.getRightsMarking());
        //保存绑卡baof的卡信息
        orderBindCardRecordRepository.findById(order.getId()).ifPresent(record -> {
            record.setFirstCardId(userBankCard.getId());
            orderBindCardRecordRepository.save(record);
        });
        response.setOutLoanSeq(outLoanSeq);
        response.setDnSts(BairongLoanStatus.LOANING.getCode());
        response.setPayMsg(BairongLoanStatus.LOANING.getDesc());
        return response;
    }





    /**
     * 【公共需求】风控通过时间校验
     *
     * @param order      订单数据
     * @param outLoanSeq 放款订单号
     * @return 返回数据
     */
    public boolean verifyOrderThirtyDayExpire(Order order, String outLoanSeq) {
        boolean returnStatus = false;
        // 【公共需求】风控通过时间校验
        // 背景：目前风控通过后，放款时候无时间校验，也就是说，对客授信通过后，无论过多久，都可以直接申请放款
        // add by hehangzheng ********
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(THIRTY);
        if (order.getCreatedTime().isBefore(failCreditDate)) {
            logger.error("【新流程】授信状态为:{},订单成功的时间为:{},orderId:{},outLoanSeq:{}", order.getOrderState(),
                order.getCreatedTime(), order.getId(), outLoanSeq);
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(BairongLoanStatus.LOAN_FAILED.getDesc());
            orderRepository.save(order);
            returnStatus = true;
        }
        return returnStatus;
    }

    /**
     * 获取协议下载数据
     *
     * @param outApplSeq    外部申请流水号
     * @param outLoanSeq   外部放款流水号
     * @param businessStage 业务阶段
     * @return 返回数据
     */
    public BairongResponseData fetchAgreementUrl(String outApplSeq, String outLoanSeq, String businessStage) {
        return null;
    }

}
