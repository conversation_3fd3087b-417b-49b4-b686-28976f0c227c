package com.maguo.loan.cash.flow.entrance.lvxin.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinResultCode;

/**
 * @ClassName Response
 * <AUTHOR>
 * @Description 绿信 加密后返回参数
 * @Date 2024/5/14 15:36
 * @Version v1.0
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LvxinResponse extends LvxinEncryptData {

    private String code;

    private String message;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static LvxinResponse success() {
        LvxinResponse result = new LvxinResponse();
        result.setCode(LvxinResultCode.SUCCESS.getCode());
        result.setMessage(LvxinResultCode.SUCCESS.getMsg());
        return result;
    }

    public static LvxinResponse success(Object data) {
        LvxinResponse result = success();
        result.setPayload(JsonUtil.toJsonString(data));
        return result;
    }

    public static LvxinResponse fail() {
        LvxinResponse result = new LvxinResponse();
        result.setCode(LvxinResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(LvxinResultCode.SYSTEM_ERROR.getMsg());
        return result;
    }

    public static LvxinResponse fail(String message) {
        LvxinResponse result = new LvxinResponse();
        result.setCode(LvxinResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(message);
        return result;
    }

    public static LvxinResponse fail(String status, String message) {
        LvxinResponse result = new LvxinResponse();
        result.setCode(status);
        result.setMessage(message);
        return result;
    }

    public static LvxinResponse exception(String msg) {
        LvxinResponse result = new LvxinResponse();
        result.setCode(LvxinResultCode.SYSTEM_ERROR.getCode());
        result.setMessage(msg);
        return result;
    }
}
