package com.maguo.loan.cash.flow.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.cash.api.dto.ProjectElementsDto;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.io.IoUtil;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.RiskConfig;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.CollisionMarketingRecord;
import com.maguo.loan.cash.flow.entity.CollisionRecord;
import com.maguo.loan.cash.flow.entity.FqlCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlLoanApplyRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.LvxinApplyRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RiskDecisionFlowConfig;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRenewedRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdLoanApplyRecord;
import com.maguo.loan.cash.flow.entrance.common.constant.CommonBaseConstant;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.fql.convert.FenQiLeConvert;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.user.UserCheckRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.CYBKResultType;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.FlowType;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.MarkStatusFlow;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.remote.core.FinCreditService;
import com.maguo.loan.cash.flow.remote.riskdata.req.BaiWeiRiskDataRequest;
import com.maguo.loan.cash.flow.remote.riskdata.req.RiskCallbackRequest;
import com.maguo.loan.cash.flow.remote.riskdata.req.RiskDataRequest;
import com.maguo.loan.cash.flow.remote.riskdata.resp.RiskDataResponse;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.AreaCodeRepository;
import com.maguo.loan.cash.flow.repository.CollisionMarketingRecordRepository;
import com.maguo.loan.cash.flow.repository.CollisionMarketingSuccessRecordRepository;
import com.maguo.loan.cash.flow.repository.CollisionRecordRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlCreditApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlLoanApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.LvxinApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.RiskDecisionFlowConfigRepository;
import com.maguo.loan.cash.flow.repository.RiskRevolvingRecordRepository;
import com.maguo.loan.cash.flow.repository.UserAccountRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserContactInfoRepository;
import com.maguo.loan.cash.flow.repository.UserDeviceRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserInfoExpandRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedTagRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdCreditApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdLoanApplyRecordRepository;
import com.maguo.loan.cash.flow.service.client.RiskControlClient;
import com.maguo.loan.cash.flow.service.event.RiskResultEvent;
import com.maguo.loan.cash.flow.service.event.RiskResultOutEvent;
import com.maguo.loan.cash.flow.util.ChineseAddressParser;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.util.ImageUtil;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import jakarta.annotation.PostConstruct;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * 风控服务
 */
@Service
public class RiskService {

    /**
     * 是否是优质客户
     */
    public static final String QUALITY_FIELD_NAME = "计算单元0";
    private static final Logger logger = LoggerFactory.getLogger(RiskService.class);


    private static final int IMG_MAX_SIZE = 500 * 1024;

    /**
     * 价格风控规则名
     */
    private static final String AMOUNT_RULE_NAME = "计算单元0";

    /**
     * 权益风控规则
     * 1. 低  2. 高
     */
    private static final String RIGHT_RULE_NAME = "计算单元1";

    /**
     * 利率风控规则
     * 1. 低  2. 高
     */
    private static final String RATE_RULE_NAME = "计算单元2";
    /**
     * 是否强制购买权益
     * 0.不强制购买权益
     * 1.强制购买权益
     */
    private static final String APPROVE_RIGHTS_FORCE = "计算单元3";

    /**
     * 风控审批期数,可以为多个,逗号分割
     */
    private static final String RISK_PERIOD = "计算单元4";

    /**
     * 权益包
     */
    private static final String RIGHTS_PACKAGE = "计算单元5";

    /**
     * 权益金额,可以为多个,逗号分割
     */
    private static final String RIGHTS_AMOUNTS = "计算单元6";

    /**
     * 客户风险评分
     * 风控输出的客户风险评分，评分区间[0,600]，以应对资方要求必传客户评分和等级的要求
     */
    private static final String APPROVE_SCORE = "计算单元7";
    /**
     * 用户风险等级
     */
    private static final String RISK_LEVEL = "计算单元8";

    /**
     * A卡评分（欺诈风险）
     */
    private static final String A_CARD_FRACTION = "计算单元12";
    /**
     * 用信意愿度（分）
     */
    private static final String WILLINGNESS = "计算单元13";
    /**
     * 客户分层标签
     */
    private static final String CUSTOMER_TIERED_LABELS = "计算单元14";

    /**
     * 额度类型
     */
    private static final String AMOUNT_TYPE = "计算单元15";
    private static final String RIGHTS_PAY_TYPE = "计算单元16";
    private static final Integer ONE = 1;
    private static final Integer TWO = 2;
    private static final Integer THREE = 3;
    private static final Integer FOUR = 4;
    private static final int MIN_CREDIT_AMT = 0;
    private static final int MAX_CREDIT_AMT = 50000;
    private static final int DAYS_INTERVAL = 30; // 时间间隔常量
    private static final String DEFAULT_RENEWED_TYPE = "-999";
    /**
     * 发起风控前部分参数特殊字符转换规则
     * 正则表达式
     */
    private static final String SYMBOLS = "[~!！@$%^&*\\[\\]{}<>？?=+￥【】……、#/\\\\.,。*_《》\\s]";
    private static final Pattern SYMBOL_PATTERN = Pattern.compile("^" + SYMBOLS + "+$");
    private static final int RENEWED_EXPIRE_DAYS = 30;
    private static final BigDecimal MAX_SUBSCRIBE_AMOUNT = new BigDecimal(100);
    private static final String QH_PROD_CODE = "15";
    private static List<FileType> fileTypes = List.of(FileType.ID_FACE, FileType.ID_NATION, FileType.ID_HEAD);

    @Autowired
    private RiskConfig riskConfig;

    @Autowired
    private MqService mqService;

    @Autowired
    private RiskControlClient riskControlClient;


    @Autowired
    private CollisionRecordRepository collisionRecordRepository;

    @Autowired
    private UserRiskRecordRepository riskRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository recordExternalRepository;
    @Autowired
    private AgreementSignatureRecordRepository signatureRecordRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private UserContactInfoRepository userContactInfoRepository;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private FileService ossFileService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;


    @Autowired
    private UserAccountRepository userAccountRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private AreaCodeRepository areaCodeRepository;
    @Autowired
    private CollisionMarketingRecordRepository collisionApplyRecordRepository;
    @Autowired
    private CollisionMarketingSuccessRecordRepository collisionMarketingSuccessRecordRepository;
    @Autowired
    private UserInfoExpandRepository userInfoExpandRepository;

    @Autowired
    private CheckService checkService;


    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private OrderRepository orderRepository;


    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private UserRenewedRecordRepository userRenewedRecordRepository;

    @Autowired
    private UserRenewedTagRepository userRenewedTagRepository;

    @Autowired
    private RiskRevolvingRecordRepository riskRevolvingRecordRepository;

    @Autowired
    private LvxinApplyRecordRepository lvxinApplyRecordRepository;

    @Autowired
    private PpdCreditApplyRecordRepository ppdCreditApplyRecordRepository;


    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CacheService cacheService;
    @Autowired
    private RiskDecisionFlowConfigRepository riskDecisionFlowConfigRepository;
    @Autowired
    private FqlCreditApplyRecordRepository fqlCreditApplyRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository RiskRecordExternalRepository;
    @Autowired
    private CreditService creditService;
    @Autowired
    private ProjectInfoService projectInfoService;
    /**
     * 续借标识查询开关
     */
    @Value("${qh.renewed.query.open:false}")
    private boolean renewedOpen;

    @Value("${qh.renewed.query.rate.limit:1}")
    private int renewedRateLimit;

    @Value("${qh.renewed.query.rate.period:3}")
    private int renewedRatePeriod;

    @Autowired
    private RedissonClient redissonClient;

    private RRateLimiter rateLimiter;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private FinCreditService finCreditService;


    @Autowired
    private PpdLoanApplyRecordRepository ppdLoanApplyRecordRepository;
    @Autowired
    private FqlLoanApplyRecordRepository fqlLoanApplyRecordRepository;


    @PostConstruct
    public void init() {
        rateLimiter = redissonClient.getRateLimiter("qh_renewed_query_rate_limiter");
        rateLimiter.setRate(RateType.OVERALL, renewedRateLimit, renewedRatePeriod, RateIntervalUnit.SECONDS);
    }

    /**
     * 用户撞库
     * @param applyRequest  请求信息
     * @param channel   渠道
     * @return 撞库状态
     */
    public CollisionRecord userCheck(UserCheckRequest applyRequest, FlowChannel channel) {
        CollisionRecord newRecord = new CollisionRecord();
        //根据证件号查找用户信息
        UserInfo userInfo = userInfoRepository.findByCertNo(applyRequest.getIdCard());
        if(Objects.nonNull(userInfo)){
            //30天 内部分控拒绝
            List<UserRiskRecord> userRiskRecords = queryThirtyDayRiskRejectRecord(userInfo.getId(), channel);
            //30天内授信失败检验
            List<Order> orders = queryThirtyDayCreditFailRecord(userInfo.getCertNo(), channel, BankChannel.CYBK);
            if (!CollectionUtils.isEmpty(userRiskRecords) || !CollectionUtils.isEmpty(orders)) {
                logger.info("绿信-长银===是否为30天内同一风控模型失败客户检验===不通过，返回撞库失败");
                newRecord.setState(AuditState.REJECT);
                newRecord.setFailReason("撞库失败");
            }else{
                logger.info("流量撞库通过，调用长银撞库");
                riskCheckCYBK(applyRequest, channel, newRecord);
            }
        }else{
            //通过证件号没有找到用户信息，直接调用长银撞库
            logger.info("绿信-长银===通过证件号没有找到用户信息，直接调用长银撞库===");
            riskCheckCYBK(applyRequest, channel, newRecord);
        }
        return collisionRecordRepository.save(newRecord);
    }

    /**
     * 调用长银撞库
     * @param applyRequest 请求参数
     * @param channel 流量渠道
     * @param newRecord 实体信息
     */
    private void riskCheckCYBK(UserCheckRequest applyRequest, FlowChannel channel, CollisionRecord newRecord) {
        //校验通过,继续资方撞库流程
        logger.info("是否为30天内同一风控模型失败客户检验===通过，继续资方撞库流程");
        newRecord.setPhone(applyRequest.getPhoneMd5());
        newRecord.setCertNo(applyRequest.getIdCardMd5());
        newRecord.setName(null);
        newRecord.setState(AuditState.INIT);
        newRecord.setFlowChannel(channel);
        //调用资方撞库，获取撞库返回信息
        RestResult<PreCreditApplyResultDto> restResult;
        try {
            // 调用fin-core撞库
            PreCreditApplyDto preCreditApply = new PreCreditApplyDto();
            preCreditApply.setMobile(applyRequest.getPhoneMd5());
            preCreditApply.setCardNo(applyRequest.getIdCardMd5());
            preCreditApply.setBankChannel(BankChannel.CYBK);
            restResult = finCreditService.preCredit(preCreditApply);
            logger.info("请求fin-core撞库结果 : {}", JsonUtil.toJsonString(restResult));
        } catch (Exception e) {
            logger.error("请求fin-core撞库异常", e);
            throw new BizException(ResultCode.BIZ_ERROR);
        }
        if (restResult.isSuccess()) {
            // 更新原因
            PreCreditApplyResultDto resultDto = restResult.getData();
            if (CYBKResultType.REJECTED.getCode().equals(resultDto.getRuleCode())) {
                newRecord.setState(AuditState.REJECT);
            } else {
                newRecord.setState(AuditState.PASS);
            }
            newRecord.setFailReason(resultDto.getRuleDesc());
        }
    }

    private boolean hasFailedRecordsInLast30Days(String userId, CollisionRecord collisionRecord) {
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(DAYS_INTERVAL);
        List<UserRiskRecord> userRiskRecords = userRiskRecordRepository.findAllByUserIdAndAmountTypeNot(userId, AmountType.REVOLVING);
        List<Order> orders = orderRepository.findAllByUserId(userId);


        //当前流量为irr36时，校验30天是否内风控拒过
        //当前流量为irr36+权益时，校验30天是否被irr36+权益拒过
        if (FlowType.IRR36 == collisionRecord.getFlowChannel().getFlowType()) {
            boolean isRejected =
                userRiskRecords.stream().anyMatch(record -> record.getApproveResult() == AuditState.REJECT && record.getCreatedTime().isAfter(thirtyDaysAgo));
            if (isRejected) {
                collisionRecord.setFailReason("存在30天内风控拒绝记录");
                return true;
            }
        } else if (FlowType.IRR36_RIGHTS == collisionRecord.getFlowChannel().getFlowType()) {
            boolean isRejected =
                userRiskRecords.stream().anyMatch(record -> record.getApproveResult() == AuditState.REJECT
                    && record.getFlowChannel().getFlowType() == FlowType.IRR36_RIGHTS
                    && record.getCreatedTime().isAfter(thirtyDaysAgo));
            if (isRejected) {
                collisionRecord.setFailReason("存在30天内风控拒绝记录");
                return true;
            }
        }

        for (Order order : orders) {
            if (order.getOrderState() == OrderState.CREDIT_FAIL && order.getUpdatedTime().isAfter(thirtyDaysAgo)) {
                collisionRecord.setFailReason("存在30天内授信失败记录");
                return true;
            }
        }

        return false;
    }

    /**
     * 用户装库(对接广告流量的撞库)
     *
     * @param phoneMd5  手机md5
     * @param certNoMd5 身份证md5
     * @return 装库状态
     */
    public boolean userCollisionMarketing(String phoneMd5, String certNoMd5, ApplyChannel applyChannel, FlowChannel channel) {
        CollisionMarketingRecord marketingRecord = new CollisionMarketingRecord();
        marketingRecord.setPhone(phoneMd5);
        marketingRecord.setCertNo(certNoMd5);
        marketingRecord.setState(AuditState.INIT);
        marketingRecord.setApplyChannel(applyChannel);
        marketingRecord.setFlowChannel(channel);
        CollisionMarketingRecord saved = collisionApplyRecordRepository.save(marketingRecord);

        return remoteCollisionCheck(saved);
    }

    private boolean remoteCollisionCheck(CollisionMarketingRecord record) {


        return record.getState() == AuditState.PASS;
    }

    public void platformRisk(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        boolean allSuccess = isRiskAgreementAllSuccess(record);
        logger.info("用户风控表 id: [{}],  所有签章结果: [{}]", record.getId(), allSuccess);
        if (allSuccess) {
            // 开始走风控系统
            platformRiskInterAsync(record);
        } else {
            mqService.submitRiskApplyDelay(record.getId());
        }
    }

    public void platformRisk(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        boolean allSuccess = isRiskAgreementAllSuccess(record);
        logger.info("用户风控表 id: [{}],  所有签章结果: [{}]", record.getId(), allSuccess);
        if (allSuccess) {
            // 开始走风控系统
            if (RiskChannel.BW.name().equals(record.getRiskChannel().name())) {
                // 分期乐风控走百维
                baiWeiplatformRiskInterAsync(record);
            }
        } else {
            mqService.submitRiskApplyOutDelay(record.getId());
        }
    }

    /**
     * 放款风控处理
     *
     * @param record
     */
    public void platformLoanRisk(final UserRiskRecord record) {
        // 开始走放款风控
        try {
            platformRiskLoanInterAsync(record);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 内部风控请求（异步）
     *
     * @param record 风控记录
     */
    private void platformRiskInterAsync(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        logger.info("平台风控开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        if (riskConfig.getRiskEnable()) {
            final String userId = record.getUserId();
            UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
            RiskDataRequest request = buildRiskCreditApplyRequest(record, userInfo);
            logger.info("平台风控开始, 请求参数: [{}]", JSON.toJSONString(request));
            String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
            RiskDataResponse riskResult = JsonUtil.convertToObject(res, RiskDataResponse.class);
            logger.info("平台风控返回： [{}]", JSON.toJSONString(riskResult));

            if (CommonBaseConstant.RISK_CODE_200.equals(riskResult.getCode())) {
                String result = riskResult.getResult().getAsyn_commit_status();
                record.setPipelineId(riskResult.getResult().getPipeline_id());
                if (!"SUCCESS".equals(result)) {
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(riskResult.getMessage());
                }
            } else {
                logger.info("风控审核接口请求异常 code: [{}] userRecordId: [{}]", riskResult.getCode(), userId);
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(riskResult.getMessage());
            }
        } else {
            riskState = AuditState.PASS;
        }
        record.setApproveResult(riskState);
        riskRecordRepository.save(record);
        if (record.getApproveResult() == AuditState.PASS) {
            //推送风控通过事件
            eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
        }
        if (record.getApproveResult() == AuditState.REJECT) {
            //推送风控拒绝事件
            eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
        }
    }

    /**
     * 百维风控请求（异步）
     *
     * @param record 风控记录
     */
    private void baiWeiplatformRiskInterAsync(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维平台风控开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        if (riskConfig.getRiskEnable()) {
            String riskId = record.getId();//风控id
            //根据风控id查询预订单记录
            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);
            if (preOrder != null) {
                String orderNo = preOrder.getOrderNo();//分期乐授信申请编号
                FqlCreditApplyRecord fqlCreditApplyRecord = fqlCreditApplyRecordRepository.findByCreditApplyId(orderNo);
                FenQiLeCreditApplyRequest request = FenQiLeConvert.INSTANCE.toCreditApplyRequest(fqlCreditApplyRecord);

                logger.info("百维平台风控开始, 请求参数: [{}]", JSON.toJSONString(request));
                //todo 百维平台授信风控地址未提供
                String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
                //todo 百维平台授信风控响应是否需要额外处理，是否与接口文档一致
                FenQiLeCreditApplyResponse riskResult = JsonUtil.convertToObject(res, FenQiLeCreditApplyResponse.class);
                logger.info("百维平台风控返回： [{}]", JSON.toJSONString(riskResult));
                record.setRemark(JSON.toJSONString(riskResult));
                //接口文档中描述要以查询结果为准，所以申请返回状态不再判断，直接调用查询，根据查询结果再去做判断
                mqService.submitRiskQueryOutDelay(record.getId());
            } else {
                riskState = AuditState.REJECT;
                record.setRiskFinalResult("预订单记录不存在");
            }
        } else {
            riskState = AuditState.PASS;
        }
        record.setApproveResult(riskState);
        recordExternalRepository.save(record);
        //此处不做调整，因在百维风控申请中，只有预订单记录不存在或者风控开关关闭的情况才会出现终态情况
        if (record.getApproveResult() == AuditState.PASS) {
            //推送外部风控通过事件
            eventPublisher.publishEvent(new RiskResultOutEvent(record.getId()));
        }
        if (record.getApproveResult() == AuditState.REJECT) {
            //推送外部风控拒绝事件
            eventPublisher.publishEvent(new RiskResultOutEvent(record.getId()));
        }
    }

    /**
     * 内部放款风控请求（异步）
     *
     * @param record 风控记录
     */
    public void platformRiskLoanInterAsync(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        logger.info("平台风控开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        final String userId = record.getUserId();
        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
        RiskDataRequest request = buildRiskLoanApplyRequest(record, userInfo);
        if (riskConfig.getRiskEnable()) {
            logger.info("平台风控开始, 请求参数: [{}]", JSON.toJSONString(request));
            String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
            RiskDataResponse riskResult = JsonUtil.convertToObject(res, RiskDataResponse.class);
            logger.info("平台风控返回： [{}]", JSON.toJSONString(riskResult));

            if (CommonBaseConstant.RISK_CODE_200.equals(riskResult.getCode())) {
                String result = riskResult.getResult().getAsyn_commit_status();
                record.setPipelineId(riskResult.getResult().getPipeline_id());
                if (!"SUCCESS".equals(result)) {
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(riskResult.getMessage());
                }
            } else {
                logger.info("风控审核接口请求异常 code: [{}] userRecordId: [{}]", riskResult.getCode(), userId);
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(riskResult.getMessage());
            }
        } else {
            riskState = AuditState.PASS;
        }

        record.setApproveResult(riskState);
        riskRecordRepository.save(record);
        Order order = orderRepository.findByRiskId(record.getId());
        if (record.getApproveResult() == AuditState.PASS) {
            if (StringUtils.isEmpty(order.getProjectCode())) {
                //没有项目编码，继续走路由
                //推送风控通过事件 - 路由资金方推送授信放款
                orderService.orderRoute(order);
            } else {
                ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                ProjectElementsDto elements = projectInfoVO.getElements();//项目要素

                //路由规则判断
                switch (elements.getCapitalRoute()){
                    case ROUTE -> {
                        //推送风控通过事件 - 路由资金方推送授信放款
                        orderService.orderRoute(order);
                    }
                    case DIRECT -> {
                        //推送风控通过事件 - 直连资金方推送授信放款
                        creditService.directApply(order);
                    }
                    default -> {
                        logger.warn("平台风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                        warningService.warn("平台风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                    }
                }
            }

        }
    }

    public RiskDataRequest buildRiskCreditApplyRequest(UserRiskRecord record, UserInfo userInfo) {

        if (null == record.getFlowChannel() || null == record.getApplyChannel()) {
            logger.error("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号：{}", record.getId());
            throw new RuntimeException("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号");
        }
        String userId = userInfo.getId();
        List<UserContactInfo> contacts = userContactInfoRepository.findByUserId(userInfo.getId());

        PreOrder preOrder = preOrderRepository.findByRiskId(record.getId()).orElseThrow();
        String projectCode = preOrder.getProjectCode();
        //开始请求风控系统
        RiskDataRequest request = new RiskDataRequest();
        chooseRiskSerialNumber(request, record, ApplyType.RISK, preOrder.getBankChannel(), preOrder.getFlowChannel());

        Map<String, Object> map = new HashMap<>();
        map.put("personId", userInfo.getCertNo());
        map.put("person_id", userInfo.getCertNo());
        map.put("person_name", preOrder.getName());
        map.put("person_mobile", preOrder.getMobile());
        map.put("business_number", preOrder.getOrderNo());
        map.put("idcard", userInfo.getCertNo());
        map.put("mobile", userInfo.getMobile());
        // 顶级字段,mobile
        map.put("userId", userId);
        map.put("cdtId", preOrder.getOrderNo());
        map.put("cusPhone", preOrder.getMobile());
        map.put("cusName", preOrder.getName());
        map.put("cusIdno", preOrder.getCertNo());
        map.put("cdtAplAmt", preOrder.getApplyAmount().toString());
        map.put("cdtAplTerm", preOrder.getApplyPeriods());
        map.put("cdtDttm", preOrder.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        map.put("flowChannel", record.getFlowChannel());
        map.put("applyChannel", record.getApplyChannel());


        // baseInfo 下的字段
        map.put("baseInfoMaritalStatus", userInfo.getMarriage());
        map.put("baseInfoCompanyPhone", userInfo.getUnitPhone());


        map.put("baseInfoLiveAddress", userInfo.getLivingAddress());

        String[] provinceCityDistrict = {"", "", ""};
        try {
            provinceCityDistrict = ChineseAddressParser.getProvinceCityDistrict(userInfo.getLivingAddress());
        } catch (Exception e) {
            logger.info("风控入参, 住宿地址: [{}] 解析异常, 设置未知", userInfo.getLivingAddress());
            provinceCityDistrict[0] = "未知";
            provinceCityDistrict[1] = "未知";
            provinceCityDistrict[2] = "未知";
        }

        map.put("baseInfoLiveProvince", provinceCityDistrict[0]);
        map.put("baseInfoLiveCity", provinceCityDistrict[1]);
        map.put("baseInfoLiveArea", provinceCityDistrict[2]);
        map.put("baseInfoProvinceCode", userInfo.getLivingProvinceCode());
        map.put("baseInfoCityCode", userInfo.getLivingCityCode());
        map.put("baseInfoAreaCode", userInfo.getLivingDistrictCode());


        map.put("baseInfoIndustry", null == userInfo.getIndustry() ? Industry.TWENTY.getCode() : userInfo.getIndustry().getCode());

        // contactInfos 下的字段  暂时先用一个
        UserContactInfo userContactInfo = contacts.get(0);
        map.put("contactInfosName", userContactInfo.getName());
        map.put("contactInfosPhone", userContactInfo.getPhone());
        map.put("contactInfosRelation", userContactInfo.getRelation().name());


        if (record.getFlowChannel().equals(FlowChannel.LVXIN)) {
            LvxinApplyRecord lvxinApplyRecord = lvxinApplyRecordRepository.findByOrderNo(preOrder.getOrderNo()).orElseThrow();

            if ("长期".equals(lvxinApplyRecord.getIdEndTime())) {
                map.put("idnoVal", lvxinApplyRecord.getIdEndTime());
            } else {
                long daysDiff = ChronoUnit.DAYS.between(LocalDate.now(),
                    LocalDate.parse(lvxinApplyRecord.getIdEndTime(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                );
                map.put("idnoVal", daysDiff);
            }


            // authInfo 下的字段
            map.put("authInfoFrontUrl", lvxinApplyRecord.getIdPositive());
            map.put("authInfoBackUrl", lvxinApplyRecord.getIdNegative());
            map.put("authInfoBorrower", lvxinApplyRecord.getLivePhoto());
            map.put("authInfoAddress", lvxinApplyRecord.getIdAddress());
            map.put("authInfoStartDueTimeOcr", lvxinApplyRecord.getIdStartTime());
            map.put("authInfoEndDueTimeOcr", lvxinApplyRecord.getIdEndTime());
            map.put("authInfoSex", lvxinApplyRecord.getIdSex());
            map.put("authInfoBirthday", extractBirthDate(lvxinApplyRecord.getIdCardNo()));
            map.put("authInfoNation", lvxinApplyRecord.getIdEthnic());
            map.put("authInfoAuthority", lvxinApplyRecord.getIdIssueOrg());
            map.put("authInfoLiveRate", lvxinApplyRecord.getFaceScore());


            String[] cityAndDistrict = {"", ""};
            try {
                cityAndDistrict = ChineseAddressParser.getCityAndDistrict(lvxinApplyRecord.getWorkUnitAddress());
            } catch (Exception e) {
                logger.info("风控入参, 工作地址: [{}] 解析异常, 设置未知", lvxinApplyRecord.getWorkUnitAddress());
                cityAndDistrict[0] = "未知";
                cityAndDistrict[1] = "未知";
            }

            map.put("baseInfoCompanyCity", cityAndDistrict[0]);
            map.put("baseInfoCompanyArea", cityAndDistrict[1]);
            map.put("baseInfoCompanyProvinceCode", lvxinApplyRecord.getWorkUnitProvinceCode());
            map.put("baseInfoCompanyCityCode", lvxinApplyRecord.getWorkUnitCityCode());
            map.put("baseInfoCompanyAreaCode", lvxinApplyRecord.getWorkUnitAreaCode());
            map.put("baseInfoInCome", lvxinApplyRecord.getMonthlyIncome());

            map.put("baseInfoEducational", lvxinApplyRecord.getEducation());
            map.put("baseInfoCompanyAddress", lvxinApplyRecord.getWorkUnitAddress());
            map.put("baseInfoCompanyName", lvxinApplyRecord.getWorkUnitName());
            map.put("baseInfoWorkType", lvxinApplyRecord.getJob());
        }

        if (record.getFlowChannel().equals(FlowChannel.PPCJDL)) {
            PpdCreditApplyRecord ppdCreditApplyRecord = ppdCreditApplyRecordRepository.findByLoanReqNo(preOrder.getOrderNo());
            if ("9999-12-31".equals(ppdCreditApplyRecord.getIdExpiryDate())) {
                map.put("idnoVal", "长期");
            } else {
                long daysDiff = ChronoUnit.DAYS.between(
                    LocalDate.now(),
                    LocalDate.parse(ppdCreditApplyRecord.getIdExpiryDate())
                );
                map.put("idnoVal", daysDiff);
            }
            map.put("authInfoEndDueTimeOcr", ppdCreditApplyRecord.getIdExpiryDate());

            UserOcr userOcr = userOcrRepository.findByUserId(record.getUserId());
            UserFace userFace = userFaceRepository.findByUserId(record.getUserId());

            // authInfo 下的字段
            map.put("authInfoFrontUrl", userOcr.getHeadOssKey());
            map.put("authInfoBackUrl", userOcr.getNationOssKey());
            map.put("authInfoBorrower", userFace.getOssKey());
            map.put("authInfoAddress", ppdCreditApplyRecord.getAddressOcr());
            map.put("authInfoStartDueTimeOcr", ppdCreditApplyRecord.getIdBeginDate());

            map.put("authInfoSex", ppdCreditApplyRecord.getSex());
            map.put("authInfoBirthday", extractBirthDate(ppdCreditApplyRecord.getIdNo()));
            map.put("authInfoNation", ppdCreditApplyRecord.getNation());
            map.put("authInfoAuthority", ppdCreditApplyRecord.getCertificationUnit());
            map.put("authInfoLiveRate", ppdCreditApplyRecord.getFaceRecoScore());


            String[] cityAndDistrict = {"", ""};
            try {
                cityAndDistrict = ChineseAddressParser.getCityAndDistrict(ppdCreditApplyRecord.getAddressOcr());
            } catch (Exception e) {
                logger.info("风控入参, 工作地址: [{}] 解析异常, 设置未知", ppdCreditApplyRecord.getAddressOcr());
                cityAndDistrict[0] = "未知";
                cityAndDistrict[1] = "未知";
            }

            map.put("baseInfoCompanyCity", cityAndDistrict[0]);
            map.put("baseInfoCompanyArea", cityAndDistrict[1]);

            map.put("baseInfoEducational", ppdCreditApplyRecord.getHighestDegree());
            map.put("baseInfoWorkType", ppdCreditApplyRecord.getOccupation());
        }

        //applyTime倒序查获userId向下所有授信订单
        List<PreOrder> preOrders = preOrderRepository.findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(userId, PreOrderState.AUDITING);
        //最近一次授信申请成功距今天数
        map.put("creditLstAplSucDays", 0);
        //最近一次授信申请拒绝距今天数
        map.put("creditLstAplFailDays", 0);
        //最近一次授信结果
        map.put("creditLstAplRes", "");
        if (!preOrders.isEmpty()) {
            map.put("creditLstAplRes", preOrders.get(0).getPreOrderState() == PreOrderState.AUDIT_PASS ? "通过" : "拒绝");
            for (PreOrder order : preOrders) {
                //最近一次授信申请成功距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_PASS)) {
                    if (map.get("creditLstAplSucDays") == null || "".equals(map.get("creditLstAplSucDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplSucDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
                //最近一次授信申请拒绝距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_REJECT)) {
                    if (map.get("creditLstAplFailDays") == null || "".equals(map.get("creditLstAplFailDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplFailDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
            }
        }

        List<Loan> loanLists = loanRepository.findByUserIdOrderByApplyTimeDesc(userId);
        //最近一次支用申请成功距今天数
        map.put("loanLstAplSucDays", 0);
        //最近一次支用申请拒绝距今天数
        map.put("loanLstAplFailDays", 0);
        //最近一次支用结果
        map.put("loanLstAplRes", "");

        if (!loanLists.isEmpty()) {
            //最近一次支用结果
            map.put("loanLstAplRes", loanLists.get(0).getLoanState() == ProcessState.SUCCEED ? "通过" : "拒绝");
            for (Loan l : loanLists) {
                //最近一次支用申请成功距今天数
                if (l.getLoanState().equals(ProcessState.SUCCEED)) {
                    if (map.get("loanLstAplSucDays") == null || "".equals(map.get("loanLstAplSucDays"))) {
                        map.put("loanLstAplSucDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
                //最近一次支用申请拒绝距今天数
                if (l.getLoanState().equals(ProcessState.FAILED)) {
                    if (map.get("loanLstAplFailDays") == null || "".equals(map.get("loanLstAplFailDays"))) {
                        map.put("loanLstAplFailDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
            }
        }
        Map<String, Object> userOverdueDays = repayPlanRepository.findUserOverdueDays(userId);
        //当前逾期天数
        map.put("reapyCurOvdNumMax", userOverdueDays.getOrDefault("currentOverdueDays", 0));
        map.put("reapyHisOvdNumMax", userOverdueDays.getOrDefault("historicalMaxOverdueDays", 0));

        //风控订单信息获取配置表方法（风控喜好：渠道维度支用次数）
        Map<String, Object> loanCounts = loanRepository.countUserIdLoansByChannel(userId);
        map.put("lxTheLoanNum", loanCounts.getOrDefault(ApplyChannel.LVXIN.name(), 0));
        map.put("ppP002TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD2.name(), 0));
        map.put("ppP001TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD3.name(), 0));
        derivativeFields(record, userId, map);
        request.setInputParamMap(map);
        return request;
    }

    private RiskDataRequest buildRiskLoanApplyRequest(UserRiskRecord record, UserInfo userInfo) {

        if (null == record.getFlowChannel() || null == record.getApplyChannel()) {
            logger.error("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号：{}", record.getId());
            throw new RuntimeException("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号");
        }

        Order order = orderRepository.findByRiskId(record.getId());
        //开始请求风控系统
        RiskDataRequest request = new RiskDataRequest();
        chooseRiskSerialNumber(request, record, ApplyType.RISK_LOAN, order.getBankChannel(), order.getFlowChannel());
        Map<String, Object> map = new HashMap<>();

        String userId = userInfo.getId();

        PpdLoanApplyRecord loanApplyRecord = ppdLoanApplyRecordRepository.findByLoanReqNo(order.getOuterOrderId());
        map.put("loanDttm", loanApplyRecord.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("loanId", loanApplyRecord.getLoanReqNo());
        map.put("term", loanApplyRecord.getLoanTerm());
        map.put("loanAmt", loanApplyRecord.getLoanAmt());
        map.put("loanReqNo", order.getOuterOrderId());

        map.put("business_number", order.getOuterOrderId());
        map.put("personId", userInfo.getCertNo());
        map.put("person_id", userInfo.getCertNo());
        map.put("person_name", userInfo.getName());
        map.put("person_mobile", userInfo.getMobile());
        map.put("bank_card_number", loanApplyRecord.getBankAcct());
        map.put("person_email", userInfo.getEmail());

        map.put("idcard", userInfo.getCertNo());
        map.put("mobile", userInfo.getMobile());
        // 顶级字段,mobile
        map.put("userId", userId);

        map.put("cdtId", order.getOuterOrderId());


        map.put("flowChannel", record.getFlowChannel());
        map.put("applyChannel", record.getApplyChannel());

        //applyTime倒序查获userId向下所有授信订单
        List<PreOrder> preOrders = preOrderRepository.findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(userId, PreOrderState.AUDITING);
        //最近一次授信申请成功距今天数
        map.put("creditLstAplSucDays", 0);
        //最近一次授信申请拒绝距今天数
        map.put("creditLstAplFailDays", 0);
        //最近一次授信结果
        map.put("creditLstAplRes", "");
        if (!preOrders.isEmpty()) {
            map.put("creditLstAplRes", preOrders.get(0).getPreOrderState() == PreOrderState.AUDIT_PASS ? "通过" : "拒绝");
            for (PreOrder preOrder : preOrders) {
                //最近一次授信申请成功距今天数
                if (preOrder.getPreOrderState().equals(PreOrderState.AUDIT_PASS)) {
                    if (map.get("creditLstAplSucDays") == null || "".equals(map.get("creditLstAplSucDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != preOrder.getApplyTime()) {
                            map.put("creditLstAplSucDays", Duration.between(preOrder.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
                //最近一次授信申请拒绝距今天数
                if (preOrder.getPreOrderState().equals(PreOrderState.AUDIT_REJECT)) {
                    if (map.get("creditLstAplFailDays") == null || "".equals(map.get("creditLstAplFailDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != preOrder.getApplyTime()) {
                            map.put("creditLstAplFailDays", Duration.between(preOrder.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
            }
        }

        List<Loan> loanLists = loanRepository.findByUserIdOrderByApplyTimeDesc(userId);
        //最近一次支用申请成功距今天数
        map.put("loanLstAplSucDays", 0);
        //最近一次支用申请拒绝距今天数
        map.put("loanLstAplFailDays", 0);
        //最近一次支用结果
        map.put("loanLstAplRes", "");

        if (!loanLists.isEmpty()) {
            //最近一次支用结果
            map.put("loanLstAplRes", loanLists.get(0).getLoanState() == ProcessState.SUCCEED ? "通过" : "拒绝");
            for (Loan l : loanLists) {
                //最近一次支用申请成功距今天数
                if (l.getLoanState().equals(ProcessState.SUCCEED)) {
                    if (map.get("loanLstAplSucDays") == null || "".equals(map.get("loanLstAplSucDays"))) {
                        map.put("loanLstAplSucDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
                //最近一次支用申请拒绝距今天数
                if (l.getLoanState().equals(ProcessState.FAILED)) {
                    if (map.get("loanLstAplFailDays") == null || "".equals(map.get("loanLstAplFailDays"))) {
                        map.put("loanLstAplFailDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
            }
        }
        Map<String, Object> userOverdueDays = repayPlanRepository.findUserOverdueDays(userId);
        //当前逾期天数
        map.put("reapyCurOvdNumMax", userOverdueDays.getOrDefault("currentOverdueDays", 0));
        map.put("reapyHisOvdNumMax", userOverdueDays.getOrDefault("historicalMaxOverdueDays", 0));

        //风控订单信息获取配置表方法（风控喜好：渠道维度支用次数）
        Map<String, Object> loanCounts = loanRepository.countUserIdLoansByChannel(userId);
        map.put("lxTheLoanNum", loanCounts.getOrDefault(ApplyChannel.LVXIN.name(), 0));
        map.put("ppP002TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD2.name(), 0));
        map.put("ppP001TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD3.name(), 0));

        derivativeFields(record, userId, map);

        request.setInputParamMap(map);
        return request;
    }

    private void derivativeFields(UserRiskRecord record, String userId, Map<String, Object> map) {
        //额度数据
        Map<String, Object> userApplyAmountHistory = preOrderRepository.findUserApplyAmountHistory(userId, record.getFlowChannel().name());
        BigDecimal transLimitCur = (BigDecimal) userApplyAmountHistory.getOrDefault("transLimitCur", BigDecimal.ZERO);
        BigDecimal transLimitUsed = (BigDecimal) userApplyAmountHistory.getOrDefault("transLimitUsed", BigDecimal.ZERO);
        BigDecimal initialAmount = (BigDecimal) userApplyAmountHistory.getOrDefault("initial_amount", BigDecimal.ZERO);
        map.put("transLimitCur", transLimitCur);
        map.put("transLimitUsed", transLimitUsed);
        map.put("transLimitUsable", transLimitCur.subtract(transLimitUsed));
        if (transLimitCur.compareTo(BigDecimal.ZERO) != 0) {
            map.put("transLimitUsedPct", transLimitUsed.divide(transLimitCur, 2, RoundingMode.HALF_UP));
        } else {
            map.put("transLimitUsedPct", BigDecimal.ZERO);
        }
        map.put("transLimitFst", initialAmount);
        //TODO: 累计额度调整次数取值逻辑
        map.put("transLimitAdjustNum", 0);

        //授信记录
        Long hisCreditApplNum = (Long) userApplyAmountHistory.get("his_credit_appl_num");
        Long hisCreditApplSucNum = (Long) userApplyAmountHistory.get("his_credit_appl_suc_num");
        Long hisCreditApplFailNum = hisCreditApplNum - hisCreditApplSucNum;
        map.put("transCreditHitAplNum", hisCreditApplNum);
        map.put("transCreditHitAplSucNum", hisCreditApplSucNum);
        map.put("transCreditHitAplFailNum", hisCreditApplFailNum);
        map.put("transCreditHitAplSucRate", hisCreditApplSucNum.doubleValue() / hisCreditApplNum.doubleValue());
        map.put("transCreditLstAplDays", userApplyAmountHistory.get("credit_appl_days"));

        //首次支用记录
        Loan firstLoan = loanRepository.findTopByUserIdAndFlowChannelAndLoanStateOrderByApplyTime(userId, FlowChannel.PPCJDL, ProcessState.SUCCEED);
        if (firstLoan != null) {
            if (firstLoan.getAmount().compareTo(BigDecimal.ZERO) != 0) {
                map.put("transFstLoanLimitUsedPct", initialAmount.divide(firstLoan.getAmount(), 2, RoundingMode.HALF_UP));
            } else {
                map.put("transFstLoanLimitUsedPct", BigDecimal.ZERO);
            }
            map.put("transFstLoanAmt", firstLoan.getAmount());
            map.put("transFstLoanAplDays", DateUtil.dateDiff(firstLoan.getApplyTime().toLocalDate(), LocalDate.now()));
            map.put("transFstLoanTerm", firstLoan.getPeriods());
            map.put("transFstLoanHour", firstLoan.getApplyTime().getHour());
        } else {
            map.put("transFstLoanLimitUsedPct", BigDecimal.ZERO);
            map.put("transFstLoanAmt", BigDecimal.ZERO);
            map.put("transFstLoanAplDays", BigDecimal.ZERO);
            map.put("transFstLoanTerm", BigDecimal.ZERO);
            map.put("transFstLoanHour", BigDecimal.ZERO);
        }


        //历史支用记录
        Long loanHitAplNum = (Long) userApplyAmountHistory.get("loan_hit_apl_num");
        Long loanHitAplSucNum = (Long) userApplyAmountHistory.get("loan_hit_apl_suc_num");
        Long loanHitAplFailNum = loanHitAplNum - loanHitAplSucNum;
        if (loanHitAplNum != 0) {
            map.put("transLoanHitAplSucRate", loanHitAplSucNum.doubleValue() / loanHitAplNum.doubleValue());
        } else {
            map.put("transLoanHitAplSucRate", 0);
        }
        map.put("transLoanHitAplNum", loanHitAplNum);
        map.put("transLoanHitAplSucNum", loanHitAplSucNum);
        map.put("transLoanHitAplFailNum", loanHitAplFailNum);

        map.put("transLoanLstAplDays", userApplyAmountHistory.getOrDefault("loan_lst_apl_days", BigDecimal.ZERO));

        Map<String, Object> userRepaySummary = orderRepository.findUserRepaySummary(userId);
        Map<String, Object> userDebtSummary = repayPlanRepository.findUserDebtSummary(userId);
        //还款行为
        map.put("transReapyHisOvdNum", userApplyAmountHistory.getOrDefault("reapy_his_ovd_num", BigDecimal.ZERO));
        map.put("transReapyHisOvdAmtMax", userApplyAmountHistory.getOrDefault("reapy_his_ovd_amt_max", BigDecimal.ZERO));
        map.put("transReapyCurOvdAmtMax", userApplyAmountHistory.getOrDefault("reapy_cur_ovd_amt_max", BigDecimal.ZERO));
        map.put("transReapyCurOvdAmtSum", userApplyAmountHistory.getOrDefault("reapy_cur_ovd_amt_sum", BigDecimal.ZERO));
        map.put("transReapyHisStlNum", userRepaySummary.getOrDefault("reapy_his_stl_num", BigDecimal.ZERO));
        map.put("transReapyLst1dStlNum", userRepaySummary.getOrDefault("reapy_lst1d_stl_num", BigDecimal.ZERO));
        map.put("transReapyLst3dStlNum", userRepaySummary.getOrDefault("reapy_lst7d_stl_num", BigDecimal.ZERO));
        map.put("transReapyLst7dStlNum", userRepaySummary.getOrDefault("reapy_lst3d_stl_num", BigDecimal.ZERO));
        map.put("transRepayTotRepaidAmt", userApplyAmountHistory.getOrDefault("repay_tot_repaid_amt", BigDecimal.ZERO));
        map.put("transRepayNmlPhasePctTerm", userApplyAmountHistory.getOrDefault("repay_nml_phase_pct_term", BigDecimal.ZERO));
        map.put("transRepayLstPayoffDays", userRepaySummary.getOrDefault("repay_lst_payoff_days", BigDecimal.ZERO));
        map.put("transRepayLstRepaidDays", userDebtSummary.getOrDefault("repay_lst_repaid_days", BigDecimal.ZERO));

        //负债
        map.put("transDebtNeedFinish3m", userDebtSummary.getOrDefault("debt_need_finish3m", BigDecimal.ZERO));
        map.put("transDebtNeedFinish6m", userDebtSummary.getOrDefault("debt_need_finish6m", BigDecimal.ZERO));
        map.put("transDebtNeedFinish12m", userDebtSummary.getOrDefault("debt_need_finish12m", BigDecimal.ZERO));
        map.put("transDebtNeedFinishall", userDebtSummary.getOrDefault("debt_need_finishall", BigDecimal.ZERO));
    }

    public static String extractBirthDate(String idCard) {
        if (idCard == null || idCard.length() < 15) {
            return ""; // 非法身份证号
        }

        if (idCard.length() == 18) {
            // 18位：第7-14位是出生日期（YYYYMMDD）
            return idCard.substring(6, 14);
        } else {
            // 15位：第7-12位是出生日期（YYMMDD），补全19前缀
            return "19" + idCard.substring(6, 12);
        }
    }


    public void platformRiskQuery(final UserRiskRecord record) {
        AuditState approveResult = record.getApproveResult();
        if (approveResult != AuditState.AUDITING) {
            return;
        }
        String riskId = record.getId();
        logger.info("user risk, credit query, risk id: {}", riskId);
        platformRiskQueryInter(record);

    }


    public void platformRiskQuery(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        AuditState approveResult = record.getApproveResult();
        if (approveResult != AuditState.AUDITING) {
            return;
        }
        String riskId = record.getId();
        logger.info("user risk, credit query, risk id: {}", riskId);
        if (RiskChannel.BW.name().equals(record.getRiskChannel().name())) {
            // 分期乐风控走百维
            baiWeiplatformRiskQueryInter(record);
        }

    }

    /**
     * 内部撞库检验
     * @param applyRequest 请求信息
     * @param channel 流量渠道
     * @return 撞库结果
     */
    public CollisionRecord internalUserCheck(UserCheckRequest applyRequest, FlowChannel channel) {
        CollisionRecord newRecord = new CollisionRecord();
        newRecord.setCertNo(applyRequest.getIdCardMd5());
        newRecord.setState(AuditState.INIT);
        newRecord.setFlowChannel(channel);
        try {
            //根据证件号查找用户信息
            UserInfo userInfo = userInfoRepository.findByCertNo(applyRequest.getIdCard());
            if(Objects.nonNull(userInfo)){
                //30天内同渠道内部风控拒绝检验
                List<UserRiskRecord> userRiskRecords = queryThirtyDayRiskRejectRecord(userInfo.getId(), channel);
                //30天内同渠道同资方授信失败检验
                List<Order> orders = queryThirtyDayCreditFailRecord(userInfo.getCertNo(), channel, BankChannel.HXBK);
                if (!CollectionUtils.isEmpty(userRiskRecords) || !CollectionUtils.isEmpty(orders)) {
                    logger.info("绿信-湖消===是否为30天内同一风控模型失败客户检验===不通过，返回撞库失败");
                    newRecord.setState(AuditState.REJECT);
                    newRecord.setFailReason("撞库失败");
                }else{
                    //检验都通过
                    newRecord.setState(AuditState.PASS);
                    newRecord.setFailReason("通过");
                }
            }else{
                //通过证件号没有找到用户信息，直接返回通过
                logger.info("绿信-湖消===通过证件号没有找到用户信息，直接返回通过===");
                newRecord.setState(AuditState.PASS);
                newRecord.setFailReason("通过");
            }
        } catch (Exception e) {
            logger.error("内部撞库检验异常, certNoMd5: {}", applyRequest.getIdCardMd5(), e);
            newRecord.setState(AuditState.REJECT);
            newRecord.setFailReason("系统异常");
        }
        return newRecord;
    }

    /**
     * 查询30天内风控拒绝记录（内部撞库专用）
     */
    private List<UserRiskRecord> queryThirtyDayRiskRejectRecord(String userId, FlowChannel channel) {
        if (StringUtil.isBlank(userId)) {
            return new ArrayList<>();
        }
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(DAYS_INTERVAL);
        return userRiskRecordRepository.queryThirtyDayRiskRejectRecord(userId, failCreditDate, AuditState.REJECT, channel);
    }

    /**
     * 查询30天内授信失败记录（内部撞库专用）
     */
    private List<Order> queryThirtyDayCreditFailRecord(String certNo, FlowChannel channel, BankChannel bankChannel) {
        if (StringUtil.isBlank(certNo)) {
            return new ArrayList<>();
        }
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(DAYS_INTERVAL);
        return orderRepository.queryThirtyDayCreditFailRecordLX(OrderState.CREDIT_FAIL, certNo, failCreditDate, channel, bankChannel);
    }



    /**
     * 调用风控查询接口
     *
     * @param record 授信记录
     */
    public void platformRiskQueryInter(final UserRiskRecord record) {


    }

    /**
     * 调用百维风控查询接口
     *
     * @param record 授信记录
     */
    public void baiWeiplatformRiskQueryInter(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维平台风控查询开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = record.getApproveResult();
        String riskId = record.getId();//风控id
        //根据风控id查询预订单记录
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);

        String orderNo = preOrder.getOrderNo();//分期乐授信申请编号
        FqlCreditApplyRecord fqlCreditApplyRecord = fqlCreditApplyRecordRepository.findByCreditApplyId(orderNo);

        CreditQueryRequest request = new CreditQueryRequest();
        request.setApplyId(fqlCreditApplyRecord.getCreditApplyId());
        request.setPartnerCode(fqlCreditApplyRecord.getPartnerCode());
        logger.info("百维平台风控查询开始, 请求参数: [{}]", JSON.toJSONString(request));
        //todo 百维平台授信风控地址未提供
        String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
        //todo 百维平台授信风控响应是否需要额外处理，是否与接口文档一致
        CreditQueryResponse riskResult = JsonUtil.convertToObject(res, CreditQueryResponse.class);
        logger.info("百维平台风控查询开始： [{}]", JSON.toJSONString(riskResult));
        record.setRemark(JSON.toJSONString(riskResult));
        // 暂时默认与接口文档返回一致
        if (0 == riskResult.getStatus()) {
            //成功
            riskState = AuditState.PASS;
        } else if (1 == riskResult.getStatus()) {
            //失败
            riskState = AuditState.REJECT;
            record.setRiskFinalResult(riskResult.getMsg());
        } else if (3 == riskResult.getStatus()) {
            //查无此单
            riskState = AuditState.REJECT;
            record.setRiskFinalResult("风控查询查无此单:" + riskResult.getMsg());
        } else {
            //处理中，异步mq查询
            mqService.submitRiskQueryOutDelay(record.getId());
        }

        record.setApproveResult(riskState);
        recordExternalRepository.save(record);
        if (record.getApproveResult() == AuditState.PASS) {
            //推送外部风控通过事件
            eventPublisher.publishEvent(new RiskResultOutEvent(record.getId()));
        }
        if (record.getApproveResult() == AuditState.REJECT) {
            //推送外部风控拒绝事件
            eventPublisher.publishEvent(new RiskResultOutEvent(record.getId()));
        }
    }


    /**
     * 调用百维风控审核结果通知接口
     *
     * @param record 授信记录
     */
    public void baiWeiplatformRiskNotice(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维风控审核结果通知开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        String riskId = record.getId();//风控id
        //根据风控id查询预订单记录
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);

        String orderNo = preOrder.getOrderNo();//分期乐授信申请编号
        FqlCreditApplyRecord fqlCreditApplyRecord = fqlCreditApplyRecordRepository.findByCreditApplyId(orderNo);

        JSONObject requestJson = new JSONObject();
        requestJson.put("applyId", fqlCreditApplyRecord.getCreditApplyId());
        requestJson.put("partnerCode", fqlCreditApplyRecord.getPartnerCode());
        requestJson.put("status", 0);
        if (record.getApproveResult() == AuditState.REJECT) {
            requestJson.put("status", 1);
            requestJson.put("msg", record.getRiskFinalResult());
        }

        logger.info("百维风控审核结果通知开始, 请求参数: [{}]", JSON.toJSONString(requestJson));
        //todo 百维平台授信风控地址未提供
        //通知接口没有返回值
        HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(requestJson));
    }

//    private boolean checkRiskResult(String riskId, WeiyanUserCreditQueryResponseDTO responseDTO) {

//    }


    @Nullable
    public UserRiskRecord findPlatformRiskRecord(String riskId) {
        return riskRecordRepository.findById(riskId).orElse(null);
    }

    @Nullable
    public UserRiskRecordExternal findPlatformRiskRecordExternal(String riskId) {
        return recordExternalRepository.findById(riskId).orElse(null);
    }

    @Nullable
    public UserRiskRecord findRiskRecordByUserId(String userId, FlowChannel flowChannel) {
        return riskRecordRepository.findTopByUserIdAndFlowChannelOrderByCreatedTimeDesc(userId, flowChannel);
    }


    public boolean isRiskAgreementAllSuccess(UserRiskRecord userRiskRecord) {
        String userRiskRecordId = userRiskRecord.getId();
        List<AgreementSignRelation> agreementSignRelations = agreementSignRelationRepository.findByRelatedId(userRiskRecordId);
        List<String> signApplyIdList = agreementSignRelations.stream().map(AgreementSignRelation::getSignApplyId).collect(Collectors.toList());

        //签章成功后会复制到user_file表
        List<UserFile> userFiles = userFileRepository.findAllById(signApplyIdList);
        return signApplyIdList.size() == userFiles.size();
    }

    public boolean isRiskAgreementAllSuccess(UserRiskRecordExternal userRiskRecord) {
        String userRiskRecordId = userRiskRecord.getId();
        List<AgreementSignRelation> agreementSignRelations = agreementSignRelationRepository.findByRelatedId(userRiskRecordId);
        List<String> signApplyIdList = agreementSignRelations.stream().map(AgreementSignRelation::getSignApplyId).collect(Collectors.toList());

        //签章成功后会复制到user_file表
        List<UserFile> userFiles = userFileRepository.findAllById(signApplyIdList);
        return true;
    }

    public String getImageBase64(String userId, FileType fileType) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
        String s = Base64.getEncoder().encodeToString(is.readAllBytes());
        IOUtils.closeQuietly(is);
        return s;
    }


    /**
     * 身份证图片大小压缩到500k以下，2k以上
     *
     * @param userId   用户id
     * @param fileType 文件类型
     * @return
     * @throws IOException
     */
    public String getCompressImageBase64(String userId, FileType fileType) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        Path tempLocalFile = Files.createTempFile("img", ".jpg");
        try (var os = Files.newOutputStream(tempLocalFile)) {
            InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
            IOUtils.copy(is, os);
            IOUtils.closeQuietly(is);
        }

        File scaledImg = ImageUtil.scaleImages(tempLocalFile.toFile(), IMG_MAX_SIZE);

        String s = Base64.getEncoder().encodeToString(Files.readAllBytes(scaledImg.toPath()));

        deleteTempFile(scaledImg);
        deleteTempFile(tempLocalFile);

        return s;
    }


    /**
     * 将文件压缩成zip然后再传
     *
     * @param userId
     * @param fileType
     * @return
     * @throws IOException
     */
    public String getZipBase64(String userId, FileType fileType, String fileName) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
        Path tempFile = Files.createTempFile("tmp", ".zip");

        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(tempFile))) {

            ZipEntry entry = new ZipEntry(fileName);
            try {
                zipOut.putNextEntry(entry);
                IoUtil.copy(is, zipOut);
                zipOut.closeEntry();
            } catch (IOException e) {
                logger.error("create file error, target: {}", fileName, e);
                // throw new RuntimeException(e);
            }

        } catch (IOException e) {
            logger.error("create file error, target: {}", fileName, e);
            // throw new RuntimeException(e);
        }
        InputStream zipFileOS = Files.newInputStream(tempFile);

        String s = Base64.getEncoder().encodeToString(zipFileOS.readAllBytes());
        IOUtils.closeQuietly(is);
        Files.deleteIfExists(tempFile);
        return s;
    }

    public String getImageIdCardMergeBase64(String userId, FileType idFaceType, FileType idNationType) throws Exception {
        UserFile faceFile = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, idFaceType);
        if (faceFile == null) {
            return null;
        }
        InputStream faceOssIs = ossFileService.getOssFile(faceFile.getOssBucket(), faceFile.getOssKey());
        Path faceLocalImg = Files.createTempFile(userId, ".jpg");
        OutputStream faceLocalOs = Files.newOutputStream(faceLocalImg);
        IOUtils.copy(faceOssIs, faceLocalOs);
        IOUtils.closeQuietly(faceOssIs);
        IOUtils.closeQuietly(faceLocalOs);

        UserFile nationFile = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, idNationType);
        if (nationFile == null) {
            return null;
        }
        InputStream nationOssIs = ossFileService.getOssFile(nationFile.getOssBucket(), nationFile.getOssKey());
        Path nationLocalImg = Files.createTempFile(userId, ".jpg");
        OutputStream nationLocalOs = Files.newOutputStream(nationLocalImg);
        IOUtils.copy(nationOssIs, nationLocalOs);
        IOUtils.closeQuietly(nationOssIs);
        IOUtils.closeQuietly(nationLocalOs);

        File targetFile = ImageUtil.mergeTwoFromFile(faceLocalImg.toFile(), nationLocalImg.toFile(), userId);

        Files.deleteIfExists(faceLocalImg);
        Files.deleteIfExists(nationLocalImg);

        if (targetFile == null) {
            return null;
        }
        File finalTarget = ImageUtil.scaleImages(targetFile, userId);

        String mergeResult = Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(finalTarget));

        Files.deleteIfExists(targetFile.toPath());
        Files.deleteIfExists(finalTarget.toPath());

        return mergeResult;
    }

    private void deleteTempFile(File tmpFile) {
        if (tmpFile == null) {
            return;
        }
        tmpFile.deleteOnExit();
    }

    private void deleteTempFile(Path tmpFile) {
        if (tmpFile == null) {
            return;
        }
        try {
            Files.delete(tmpFile);
        } catch (IOException e) {
            // no throw
        }
    }

    private String buildHuaRongAuthIdx(String id) {
        return id + "AND111100000000";
    }

    /**
     * 续借查询
     *
     * @param renewedRecordId 续借标识记录id
     */
    public void renewedQuery(String renewedRecordId) {
        UserRenewedRecord renewedRecord = userRenewedRecordRepository.findById(renewedRecordId).orElseThrow();
        // 查询借款信息
        Loan loan = loanRepository.findByOrderId(renewedRecord.getOrderId());

        boolean renewedFlag = false;

        if (renewedOpen) {
            // 查询用户信息
            UserInfo userInfo = userInfoRepository.findById(renewedRecord.getUserId()).orElseThrow();
            // 查询设备信息
            UserDevice userDevice = userDeviceRepository.findByUserId(renewedRecord.getUserId());
            // 查询orc信息
            UserOcr ocr = userOcrRepository.findByUserId(renewedRecord.getUserId());
            // 查询联系人信息
            List<UserContactInfo> userContacts = userContactInfoRepository.findByUserId(userInfo.getId());
            if (CollectionUtils.isEmpty(userContacts)) {
                // 联系人为空时直接返回不再查询风控
                logger.info("续借标识查询联系人为空, creditId: {}", renewedRecord.getId());
                renewedRecord.setFailReason("联系人为空不查询");
                renewedRecord.setStatus(MarkStatusFlow.EXCEPTION);
                userRenewedRecordRepository.save(renewedRecord);
                return;
            }

        }


    }

//    public void chooseRiskSerialNumber(RiskDataRequest request, UserRiskRecord record, ApplyType applyType) {
//        FlowChannel flowChannel = record.getFlowChannel();
//        if (flowChannel == FlowChannel.LVXIN) {
//            request.setCallSerialNumber(riskConfig.getCreditApplyLvxinSerialNumber());
//            request.setSystemKey(riskConfig.getCreditApplyLvxinSystemKey());
//        } else if (flowChannel == FlowChannel.PPCJDL) {
//            if (applyType == ApplyType.RISK) {
//                request.setCallSerialNumber(riskConfig.getCreditApplyPpdSerialNumber());
//                request.setSystemKey(riskConfig.getCreditApplyPpdSystemKey());
//            } else if (applyType == ApplyType.RISK_LOAN) {
//                request.setCallSerialNumber(riskConfig.getLoanApplyPpdSerialNumber());
//                request.setSystemKey(riskConfig.getLoanApplyPpdSystemKey());
//            }
//
//        }
//    }

    public void chooseRiskSerialNumber(RiskDataRequest request, UserRiskRecord record, ApplyType applyType, BankChannel bankChannel, FlowChannel flowChannel) {
        String key = RedisKeyConstants.RISK_DECISION_KEY;
        List<RiskDecisionFlowConfig> configs = cacheService.getList(key,RiskDecisionFlowConfig.class);
        if (configs == null) {
            configs = riskDecisionFlowConfigRepository.findAll();
            if (CollectionUtils.isEmpty(configs)) {
                logger.info("risk_decision_key: 风控决策流配置未配置");
                warningService.warn("risk_decision_key: 风控决策流配置未配置");
                throw new LvxinBizException("风控决策流配置未配置");
            }
            cacheService.put(key, configs);
        }
        for (RiskDecisionFlowConfig config : configs) {
            if (config.getApplyChannel().equals(record.getApplyChannel())
                && config.getStage().equals(applyType)
                && config.getBankChannel().equals(bankChannel)
                && config.getFlowChannel().equals(flowChannel)) {
                request.setCallSerialNumber(config.getDecisionFlowNo());
                request.setSystemKey(config.getSecretKey());
                break; // 如果找到匹配项就可以退出循环
            }
        }
        if (StringUtils.isEmpty(request.getCallSerialNumber()) || StringUtils.isEmpty(request.getSystemKey())) {
            warningService.warn("risk_decision_key: 风控决策流配置未配置");
            throw new LvxinBizException("风控决策流配置未配置");
        }
    }

    public String riskCallback(RiskCallbackRequest request) {
        logger.info("风控回调接口请求参数：{}", JsonUtil.toJsonString(request));
        try {
            AuditState riskState = AuditState.PASS;
            String pipelineId = request.getResult().getPipeline_id();
            UserRiskRecord record = userRiskRecordRepository.findByPipelineId(pipelineId).orElseThrow();
            if (CommonBaseConstant.RISK_CODE_200.equals(request.getCode())) {
                RiskCallbackRequest.InnerResult result = request.getResult().getResult();
                if (!"pass".equals(result.getDecision())) {
                    riskState = AuditState.REJECT;
                    if (result.getCustom_str() != null) {
                        record.setRiskFinalResult(result.getCustom_str().containsKey("risk_reason") ? result.getCustom_str().getString("risk_reason") : "");
                    } else {
                        record.setRiskFinalResult("风控拒绝，未知原因");
                    }
                }
            } else {
                logger.info("风控审核接口回调异常 code: [{}] userRecordId: [{}]", request.getCode(), record.getUserId());
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(request.getMessage());
            }
            record.setApproveResult(riskState);
            riskRecordRepository.save(record);
            if (record.getApplyType() == ApplyType.RISK) {
                if (record.getApproveResult() == AuditState.PASS) {
                    //推送风控通过事件
                    eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
                }
                if (record.getApproveResult() == AuditState.REJECT) {
                    //推送风控拒绝事件
                    eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
                }
                // 处理中延迟查询
                if (record.getApproveResult() == AuditState.AUDITING) {
                    mqService.submitRiskQueryDelay(record.getId());
                }
            } else if (record.getApplyType() == ApplyType.RISK_LOAN) {
                Order order = orderRepository.findByRiskId(record.getId());
                if (record.getApproveResult() == AuditState.PASS) {
                    //推送风控通过事件 - 路由资金方推送授信放款
                    orderService.orderRoute(order);
                } else if (record.getApproveResult() == AuditState.REJECT) {
                    order.setOrderState(OrderState.LOAN_FAIL);
                    order.setRemark("支用风控拒绝");
                    orderRepository.save(order);
                }
            }
        } catch (Exception e) {
            logger.error("风控回调处理异常:{},{}", JsonUtil.toJsonString(request), e.getMessage());
            return "fail";
        }
        return "success";
    }

    /**
     * 分期乐调用百维风控审核申请
     *
     * @param record
     */
    public void baiWeiPlatformLoanRisk(final UserRiskRecordExternal record) {
        // 开始调用百维风控审核申请
        try {
            baiWeiPlatformRiskLoanInterAsync(record);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 百维风控审核申请（异步）
     *
     * @param record 风控记录
     */
    public void baiWeiPlatformRiskLoanInterAsync(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维：申请风控审核开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        String orderNo = "";
        if (riskConfig.getRiskEnable()) {
            String riskId = record.getId();//风控id
            //根据风控id查询预订单
            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);
            if (null == preOrder) {
                riskState = AuditState.REJECT;
                record.setRiskFinalResult("预订单记录不存在");
            } else {
                orderNo = preOrder.getOrderNo();
                FqlLoanApplyRecord fqlLoanApplyRecord = fqlLoanApplyRecordRepository.findByApplyId(orderNo);
                //百维请求接口参数组装
                BaiWeiRiskDataRequest request = BaiWeiBuildRiskLoanApplyRequest(fqlLoanApplyRecord);
                logger.info("百维：申请风控审核开始, 请求参数: [{}]", JSON.toJSONString(request));
                //调用百维API：支用申请风控审核申请
                String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
                RiskDataResponse riskResult = JsonUtil.convertToObject(res, RiskDataResponse.class);
                logger.info("百维：申请风控审核返回： [{}]", JSON.toJSONString(riskResult));

                mqService.submitBaiWeiRiskQuery(riskId);
                //if (CommonBaseConstant.RISK_CODE_200.equals(riskResult.getCode())) {
                //    String result = riskResult.getResult().getAsyn_commit_status();
                //    record.setPipelineId(riskResult.getResult().getPipeline_id());
                //    if (!"SUCCESS".equals(result)) {
                //        riskState = AuditState.REJECT;
                //        record.setRiskFinalResult(riskResult.getMessage());
                //    }
                //} else {
                //    logger.info("风控审核接口请求异常 code: [{}] 用户风控记录id: [{}]", riskResult.getCode(),riskId);
                //    riskState = AuditState.REJECT;
                //    record.setRiskFinalResult(riskResult.getMessage());
                //}
            }
        } else {
            riskState = AuditState.PASS;
        }
        record.setApproveResult(riskState);
        RiskRecordExternalRepository.save(record);
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(orderNo, FlowChannel.FQLQY001);
        if (null != order) {
            if (record.getApproveResult() == AuditState.PASS) {
                if (StringUtils.isEmpty(order.getProjectCode())) {
                    //没有项目编码，继续走路由
                    //推送风控通过事件 - 路由资金方推送授信放款
                    orderService.orderRoute(order);
                } else {
                    ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                    ProjectElementsDto elements = projectInfoVO.getElements();//项目要素

                    //路由规则判断
                    switch (elements.getCapitalRoute()){
                        case ROUTE -> {
                            //推送风控通过事件 - 路由资金方推送授信放款
                            orderService.orderRoute(order);
                        }
                        case DIRECT -> {
                            //推送风控通过事件 - 直连资金方推送授信放款
                            creditService.directApply(order);
                        }
                        default -> {
                            logger.warn("百维外部风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                            warningService.warn("百维外部风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                        }
                    }
                }

            }
        }
    }

    /**
     * 百维风控审核申请
     * 参数拼接
     *
     * @param record 分期乐风控记录
     */
    private BaiWeiRiskDataRequest BaiWeiBuildRiskLoanApplyRequest(FqlLoanApplyRecord record) {
        BaiWeiRiskDataRequest request = new BaiWeiRiskDataRequest();
        if (null == record) {
            logger.error("百维风控审核入参, 缺少必要入参：", record.getApplyId());
            throw new RuntimeException("风控入参, 缺少必要入参：对应风控标识流水号");
        }
        request.setApplyId(record.getApplyId());
        request.setPartnerCode(record.getPartnerCode());
        request.setOrderType(record.getOrderType());
        request.setLoanPrincipal(record.getLoanPrincipal());
        request.setLoanTerm(record.getLoanTerm());
        request.setAnnualRate(record.getAnnualRate());
        request.setLoanUse(record.getLoanUse());
        request.setMobileNo(record.getMobileNo());
        request.setDebitAccountName(record.getDebitAccountName());
        request.setDebitOpenAccountBank(record.getDebitOpenAccountBank());
        request.setDebitAccountNo(record.getDebitAccountNo());
        request.setPaymentBankCode(record.getPaymentBankCode());
        request.setDebitCnaps(record.getDebitCnaps());
        request.setRepayType(record.getRepayType());
        return request;
    }

    /**
     * 分期乐调用百维风控审核申请
     *
     * @param record
     */
    public void baiWeiPlatformLoanRiskQuery(final UserRiskRecordExternal record) {
        // 开始调用百维风控审核申请
        try {
            baiWeiPlatformRiskLoanInterAsync(record);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void baiWeiPlatformRiskQuery(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        AuditState approveResult = record.getApproveResult();
        if (approveResult != AuditState.AUDITING) {
            return;
        }
        String riskId = record.getId();
        logger.info("user risk, loan query, risk id: {}", riskId);
        //风控审核结果查询
        baiWeiPlatformRiskQueryInter(record);
    }

    /**
     * 百维风控审核结果查询（异步）
     *
     * @param record 风控记录
     */
    public void baiWeiPlatformRiskQueryInter(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维：风控审核结果开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        String orderNo = "";
        if (riskConfig.getRiskEnable()) {
            String riskId = record.getId();//风控id
            //根据风控id查询预订单
            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);
            if (null == preOrder) {
                riskState = AuditState.REJECT;
                record.setRiskFinalResult("预订单记录不存在");
            } else {
                orderNo = preOrder.getOrderNo();
                FqlLoanApplyRecord fqlLoanApplyRecord = fqlLoanApplyRecordRepository.findByApplyId(orderNo);
                //百维请求接口参数组装
                JSONObject request = new JSONObject();
                request.put("applyId", fqlLoanApplyRecord.getApplyId());
                request.put("partnerCode", fqlLoanApplyRecord.getPartnerCode());
                logger.info("调用百维：风控审核结果开始, 请求参数: [{}]", JSON.toJSONString(request));
                //调用百维API：支用申请风控审核申请
                String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
                JSONObject riskResult = JsonUtil.convertToObject(res, JSONObject.class);
                logger.info("百维：风控审核结果返回： [{}]", JSON.toJSONString(riskResult));
                if (null == riskResult) {
                    logger.error("调用百维：风控审核结果查询异常：返回结果：{}", riskResult);
                    throw new BizException(riskResult.getString("msg"), ResultCode.BAIWEI_RISK_RECORD_ERROR);
                }
                Integer loanStatus = riskResult.getInteger("loanStatus");
                String msg = riskResult.getString("msg");
                if (0 == loanStatus) {
                    //成功
                    riskState = AuditState.PASS;
                } else if (1 == loanStatus) {
                    //失败
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(msg);
                    //风控审核被拒,通知百维
                    mqService.submitBaiWeiRiskNotify(riskId);
                } else if (2 == loanStatus) {
                    //查无此单
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(msg);
                    //查无此单 按照被拒逻辑 通知百维
                    mqService.submitBaiWeiRiskNotify(riskId);
                    //重新发起支用申请风控审核方法
                    //mqService.submitRiskLoanApply(riskId);
                } else {
                    //99=处理中
                    //异步mq 重新调用当前方法获取结果
                    mqService.submitBaiWeiRiskQuery(riskId);
                }
            }
        } else {
            riskState = AuditState.PASS;
        }
        record.setApproveResult(riskState);
        RiskRecordExternalRepository.save(record);
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(orderNo, FlowChannel.FQLQY001);
        if (null != order) {
            if (record.getApproveResult() == AuditState.PASS) {

                if (StringUtils.isEmpty(order.getProjectCode())) {
                    //没有项目编码，继续走路由
                    //推送风控通过事件 - 路由资金方推送授信放款
                    orderService.orderRoute(order);
                } else {
                    ProjectInfoDto projectInfoVO = projectInfoService.queryProjectInfo(order.getProjectCode());
                    ProjectElementsDto elements = projectInfoVO.getElements();//项目要素

                    //路由规则判断
                    switch (elements.getCapitalRoute()){
                        case ROUTE -> {
                            //推送风控通过事件 - 路由资金方推送授信放款
                            orderService.orderRoute(order);
                        }
                        case DIRECT -> {
                            //推送风控通过事件 - 直连资金方推送授信放款
                            creditService.directApply(order);
                        }
                        default -> {
                            logger.warn("百维外部风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                            warningService.warn("百维外部风控，没有对应的路由方式，riskId：[{}]，orderId：[{}]", record.getId(), order.getId());
                        }
                    }
                }
            }
        }
    }

    /**
     * 百维：支用风控审核结果通知（异步）
     *
     * @param record 风控记录
     */
    public void baiWeiPlatformRiskNotify(final UserRiskRecordExternal record) throws HttpException, JsonProcessingException {
        logger.info("百维：风控审核结果通知开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        String orderNo = "";
        if (riskConfig.getRiskEnable()) {
            String riskId = record.getId();//风控id
            //根据风控id查询预订单
            PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElse(null);
            if (null == preOrder) {
                record.setRiskFinalResult("预订单记录不存在");
            } else {
                orderNo = preOrder.getOrderNo();
                FqlLoanApplyRecord fqlLoanApplyRecord = fqlLoanApplyRecordRepository.findByApplyId(orderNo);
                Loan loan = loanRepository.findByOuterLoanId(orderNo);
                //百维请求接口参数组装
                JSONObject request = new JSONObject();
                request.put("applyId", fqlLoanApplyRecord.getApplyId());
                request.put("partnerCode", fqlLoanApplyRecord.getPartnerCode());
                request.put("loanNo", loan.getId());
                request.put("loanStatus", loan.getLoanState());
                request.put("msg", loan.getFailReason());
                logger.info("调用百维：风控审核结果通知开始, 请求参数: [{}]", JSON.toJSONString(request));
                //调用百维API：支用申请风控审核申请
                String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
                JSONObject riskResult = JsonUtil.convertToObject(res, JSONObject.class);
                logger.info("百维：风控审核结果通知返回： [{}]", JSON.toJSONString(riskResult));
            }
        }
    }

}
