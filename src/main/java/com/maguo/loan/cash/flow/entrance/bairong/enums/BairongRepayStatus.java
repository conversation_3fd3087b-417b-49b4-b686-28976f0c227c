package com.maguo.loan.cash.flow.entrance.bairong.enums;


/**
 * 还款结果状态
 * <AUTHOR>
 */
public enum BairongRepayStatus {

    SUCCESS("01", "还款成功"),
    FAIL("02", "还款失败"),
    PROCESSING("03", "处理中"),
    ERROR("10", "系统异常"),
    CLEARED("20", "项目(当期)已结清"),
    CUSTOM_REPAY_RECORD_IS_NULL_ERROR("101", "对客还款记录不存在"),
    BANK_REPAY_RECORD_IS_NULL_ERROR("102", "对资还款记录不存在");

    private final String code;
    private final String desc;

    BairongRepayStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
