package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * @ClassName BaiRongDeductAgreementNoSyncRequest
 * <AUTHOR>
 * @Description 扣款协议号同步请求参数
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
@Data
public class BairongDeductAgreementNoSyncRequest {
    /**
     * 渠道申请流水号
     */
    @NotBlank(message = "渠道申请流水号不能为空")
    private String outSignSeq;

    /**
     * 资金方授信流水号
     */
    private String applCde;

    /**
     * 渠道放款流水号
     */
    private String outLoanSeq;

    /**
     * 扣款账户号
     */
    @NotBlank(message = "扣款账户号不能为空")
    private String acctNo;

    /**
     * 扣款账户户名
     */
    @NotBlank(message = "扣款账户户名不能为空")
    private String acctName;

    /**
     * 证件类型
     * 必填（20：身份证）
     */
    @NotBlank(message = "证件类型不能为空")
    private String idType;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String idNo;

    /**
     * 银行预留手机号
     */
    @NotBlank(message = "银行预留手机号不能为空")
    @Pattern(regexp = "^[0-9]{11,13}$", message = "银行预留手机号格式不正确（应为11-13位数字）")
    private String telNo;

    /**
     * 签约渠道
     * 必填（1：通联，2：易宝）
     */
    @NotBlank(message = "签约渠道不能为空")
    @Pattern(regexp = "^[12]$", message = "签约渠道仅支持1（通联）或2（易宝）")
    private String payChannel;

    /**
     * 扣款账户开户行编码
     */
    @NotBlank(message = "扣款账户开户行编码不能为空")
    private String acctBankCode;

    /**
     * 支付通道扣款协议号
     */
    @NotBlank(message = "支付通道扣款协议号不能为空")
    private String signNo;

    /**
     * 校验applCde与outLoanSeq至少有一个非空
     * @return true-校验通过，false-两个字段都为空
     */
    public boolean isApplOrLoanSeqValid() {
        return (applCde != null && !applCde.trim().isEmpty())
            || (outLoanSeq != null && !outLoanSeq.trim().isEmpty());
    }

}
