package com.maguo.loan.cash.flow.entrance.bairong.filter;

import com.alibaba.cloud.commons.io.IOUtils;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.bairong.config.BairongConfig;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongRequestData;
import com.maguo.loan.cash.flow.entrance.bairong.dto.BairongResponseData;
import com.maguo.loan.cash.flow.entrance.bairong.exception.BairongException;
import com.maguo.loan.cash.flow.entrance.bairong.utils.BairongEncryptDataUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 百融加解密过滤器
 * @date 2025/9/10 14:18
 */
public class BairongEncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(BairongEncryptFilter.class);

    private final BairongConfig config;

    public BairongEncryptFilter(BairongConfig encryptFilterConfig) {
        this.config = encryptFilterConfig;
    }

    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("百融 入参原始报文: {}, 是否跳过验签: {}, URL: {}", requestStr, config.isSkipSignVerify(), req.getRequestURL());

        try {
            BairongRequestData bairongRequestData = JsonUtil.convertToObject(requestStr, BairongRequestData.class);
            String decryptedJson;

            if (config.isSkipSignVerify() || "test".equals(bairongRequestData.getChannel())) {
                // 测试模式或跳过验签时直接使用加密的JSON
                decryptedJson = BairongEncryptDataUtils.decryptJson(bairongRequestData.getJson(), config.getAesKey());
            } else {
                // 正常模式：验签后解密
                boolean signResult = BairongEncryptDataUtils.checkSignAndDecrypt(bairongRequestData, config.getAesKey(), config.getSignKey());
                if (!signResult) {
                    logger.error("百融验签失败, 原始报文: {}", requestStr);
                    throw BairongException.SIGN_VERIFY_FAIL;
                }

                logger.info("百融 入参验签成功: {}", JsonUtil.toJsonString(bairongRequestData));
                decryptedJson = BairongEncryptDataUtils.decryptJson(bairongRequestData.getJson(), config.getAesKey());
            }

            req.setAttribute("decryptedRequestBody", decryptedJson);
            req.setAttribute("originalRequestData", bairongRequestData);
            logger.info("百融 入参解密后业务数据: {}", decryptedJson);

            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, decryptedJson.getBytes(StandardCharsets.UTF_8));
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);

            chain.doFilter(requestWrapper, responseWrapper);

            // 处理响应
            res.setCharacterEncoding("utf-8");
            res.setContentType("application/json");

            byte[] contentBytes = responseWrapper.getContentAsByteArray();
            String responseStr = new String(contentBytes, StandardCharsets.UTF_8);
            logger.info("百融 出参原始报文: {}, URL: {}", responseStr, req.getRequestURL());

            // 构建加密响应
            BairongResponseData encryptedResponse = new BairongResponseData();
            encryptedResponse.setChannel(config.getChannelCode());
            encryptedResponse.setAppId(config.getAppId());
            encryptedResponse.setJson(responseStr);
            encryptedResponse.setRandomStr(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            if (!config.isSkipSignVerify()) {
                // 加密和签名响应
                BairongEncryptDataUtils.signAndEncrypt(encryptedResponse, config.getAesKey(), config.getSignKey());
            }

            String jsonString = JsonUtil.toJsonString(encryptedResponse);
            logger.info("百融 出参加密后报文: {}, URL: {}", jsonString, req.getRequestURL());

            res.setContentLength(jsonString.getBytes(StandardCharsets.UTF_8).length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(jsonString.getBytes(StandardCharsets.UTF_8));
            outputStream.flush();

        } catch (Exception e) {
            logger.error("百融 调用异常", e);
            handleException(res, e);
        }
    }

    /**
     * 处理异常响应
     */
    private void handleException(HttpServletResponse res, Exception e) throws IOException {
        res.setCharacterEncoding("utf-8");
        res.setContentType("application/json");

        String errorMsg = e.getMessage();
        if (e instanceof BairongException) {
            BairongException bizEx = (BairongException) e;
            errorMsg = "错误码: " + bizEx.getErrorCode() + ", 错误信息: " + bizEx.getMessage();
        }

        BairongResponseData errorResponse = BairongResponseData.exception(errorMsg, config.getChannelCode(), config.getAppId());
        String errorJson = JsonUtil.toJsonString(errorResponse);

        byte[] contentBytes = errorJson.getBytes(StandardCharsets.UTF_8);
        res.setContentLength(contentBytes.length);

        ServletOutputStream outputStream = res.getOutputStream();
        outputStream.write(contentBytes);
        outputStream.flush();
    }
}
