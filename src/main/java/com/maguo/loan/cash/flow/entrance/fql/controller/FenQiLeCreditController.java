package com.maguo.loan.cash.flow.entrance.fql.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.enums.CapitalRoute;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.fql.service.FenQiLeService;
import com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.service.CreditValidationService;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 分期乐授信接口
 * @Author: abai
 * @Date: 2025-8-8 下午 02:07
 */
@RestController
@RequestMapping("/fenQiLe/api")
public class FenQiLeCreditController extends FqlApiValidator {
    private static final Logger logger = LoggerFactory.getLogger(FenQiLeCreditController.class);

    @Autowired
    private FenQiLeService fenQiLeService;

    @Autowired
    private CreditValidationService creditValidationService;

    @Autowired
    private ProjectCodeMapper projectCodeMapper;

    @Autowired
    private ProjectInfoService projectInfoService;

    @PostMapping("/creditApply")
    public FenQiLeCreditApplyResponse creditApply(@RequestBody @Valid FenQiLeCreditApplyRequest request, BindingResult bindingResult) {
        try {
            // 参数校验
            validate(bindingResult);
            //为空给0
            if (StringUtils.isEmpty(request.getUserOccupation())){
                request.setUserOccupation("0");
            }
            // 公共参数校验
            String validateMessage = validateCommonParameters(request);

            if (StringUtils.isNotBlank(validateMessage)) {
                logger.warn("授信申请 pre-check 失败, 原因: {}", validateMessage);
                return FenQiLeCreditApplyResponse.fail(validateMessage);
            }
            return fenQiLeService.creditApply(request);
        }catch (Exception e){
            logger.error("分期乐授信失败,请联系管理员", e);
            return FenQiLeCreditApplyResponse.fail("申请失败，系统异常");
        }
    }

    /**
     * 公共参数校验
     */
    private String validateCommonParameters(FenQiLeCreditApplyRequest request) {
        // 获取项目编码
        String projectCode = getProjectCode(request);
        ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
        if (projectElements == null){
            return "未配置项目信息";
        }
        if (StringUtils.isNotBlank(projectCode)) {
            String capitalRoute = projectElements.getElements().getCapitalRoute().getCode();
            //判断资方路由是直连还是路由
            if (Objects.equals(capitalRoute, CapitalRoute.DIRECT.getCode())) {
                // 校验授信黑暗期
                if (creditValidationService.isInCreditDarkHours(projectElements)) {
                    return "当前时间在授信黑暗期内，请稍后再试";
                }
            }
            // 校验日授信限额
            if (creditValidationService.isDailyCreditLimitExceeded(projectElements, request.getCreditAmount())) {
                return "日授信限额超限";
            }
            // 校验年龄范围
            if (!creditValidationService.isAgeInRange(projectElements, request.getAge())) {
                return "年龄不在允许范围内";
            }
            // 校验可提现范围
            if (!creditValidationService.isAmountInDrawableRange(projectElements, request.getCreditAmount())) {
                return "申请金额不在可提现范围内";
            }
        }

        // 基础校验
        return validateBasicParameters(request);
    }

    /**
     * 基础参数校验
     */
    private String validateBasicParameters(FenQiLeCreditApplyRequest request) {
        BigDecimal loanAmt = request.getCreditAmount();
        if(loanAmt == null){
            return "放款金额不能为空。";
        }

        // 验证范围 [1000, 50000]
        boolean inRange = loanAmt.compareTo(BigDecimal.valueOf(1000)) >= 0 && loanAmt.compareTo(BigDecimal.valueOf(50000)) <= 0;
        if(!inRange){
            return "放款金额必须大于等于1000且小于等于50000。";
        }

        String idExpiryDate = request.getIdCardExpireDate();
        LocalDate expiryDate = LocalDate.parse(idExpiryDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        if(LocalDate.now().isAfter(expiryDate)) {
            return "放款金额必须大于等于1000且小于等于50000。";
        }

        boolean isStandardAge = request.getAge() >= 22 && request.getAge() <= 55;
        if(!isStandardAge){
            throw new ValidationException("申请年龄验证不通过,需要在22到55之间。", ResultCode.PARAM_ILLEGAL);
        }
        return null;
    }

    /**
     * 获取项目编码
     */
    private String getProjectCode(FenQiLeCreditApplyRequest request) {
        Map<String, String> params = new HashMap<>();
        params.put("flowSource", FlowChannel.FQLQY001.name());
        params.put("partnerCode", request.getPartnerCode());
        String projectCode = projectCodeMapper.getProjectCode("FLOW", params);

        return projectCode;
    }

    @PostMapping("/creditQuery")
    public CreditQueryResponse creditQuery(@RequestBody @Valid CreditQueryRequest request, BindingResult bindingResult) {
        try {
            // 必填参数校验
            validate(bindingResult);
            //验证
//            ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getPartnerCode());
//            if (applyChannel == null) {
//                CreditQueryResponse response = new CreditQueryResponse();
//                response.setStatus(1);
//                response.setMsg("未知渠道号");
//                return response;
//            }
            // 业务逻辑
            return fenQiLeService.creditResultQuery(request);
        } catch (Exception e) {
            logger.error("分期乐授信申请查询失败", e);
            return CreditQueryResponse.fail("查询失败，系统异常");
        }
    }
}
