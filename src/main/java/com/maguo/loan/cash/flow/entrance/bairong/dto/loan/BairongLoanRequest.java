package com.maguo.loan.cash.flow.entrance.bairong.dto.loan;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

public class BairongLoanRequest {
    // 对应 bairong_loan_apply 表
    @NotBlank(message = "渠道放款流水号不能为空")
    private String outLoanSeq; // 渠道放款流水号

    @NotBlank(message = "贷款品种不能为空")
    private String loanTyp; // 贷款品种

    @NotNull(message = "申请放款金额不能为空")
    private BigDecimal dnAmt; // 申请放款金额

    @NotNull(message = "申请期数不能为空")
    private Integer applyTnr; // 申请期数

    @NotBlank(message = "还款方式不能为空")
    private String mtdCde; // 还款方式

    @NotBlank(message = "贷款用途不能为空")
    private String purpose; // 贷款用途

    @NotBlank(message = "每期还款日不能为空")
    private String dueDayOpt; // 每期还款日

    // 对应 bairong_apply_basic_info 表
    @NotBlank(message = "申请人名称不能为空")
    private String custName; // 申请人名称

    @NotBlank(message = "申请人证件类型不能为空")
    private String idTyp; // 申请人证件类型

    @NotBlank(message = "申请人证件号不能为空")
    private String idNo; // 申请人证件号

    @NotBlank(message = "证件有效期开始日不能为空")
    private String idNoStartDate; // 证件有效期开始日

    @NotBlank(message = "证件有效期截止日不能为空")
    private String idNoEndDate; // 证件有效期截止日

    @NotBlank(message = "身份证发证机构不能为空")
    private String idOrgan; // 身份证发证机构

    @NotBlank(message = "手机号不能为空")
    private String indivMobile; // 手机号

    @NotBlank(message = "婚姻状况不能为空")
    private String indivMarital; // 婚姻状况

    @NotBlank(message = "最高学历不能为空")
    private String indivEdu; // 最高学历

    @NotBlank(message = "户籍地址不能为空")
    private String idCardAddress; // 户籍地址

    @NotBlank(message = "民族不能为空")
    private String nation; // 民族

    private String channelCode;

    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getLoanTyp() {
        return loanTyp;
    }

    public void setLoanTyp(String loanTyp) {
        this.loanTyp = loanTyp;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public String getMtdCde() {
        return mtdCde;
    }

    public void setMtdCde(String mtdCde) {
        this.mtdCde = mtdCde;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getDueDayOpt() {
        return dueDayOpt;
    }

    public void setDueDayOpt(String dueDayOpt) {
        this.dueDayOpt = dueDayOpt;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoStartDate() {
        return idNoStartDate;
    }

    public void setIdNoStartDate(String idNoStartDate) {
        this.idNoStartDate = idNoStartDate;
    }

    public String getIdNoEndDate() {
        return idNoEndDate;
    }

    public void setIdNoEndDate(String idNoEndDate) {
        this.idNoEndDate = idNoEndDate;
    }

    public String getIdOrgan() {
        return idOrgan;
    }

    public void setIdOrgan(String idOrgan) {
        this.idOrgan = idOrgan;
    }

    public String getIndivMobile() {
        return indivMobile;
    }

    public void setIndivMobile(String indivMobile) {
        this.indivMobile = indivMobile;
    }

    public String getIndivMarital() {
        return indivMarital;
    }

    public void setIndivMarital(String indivMarital) {
        this.indivMarital = indivMarital;
    }

    public String getIndivEdu() {
        return indivEdu;
    }

    public void setIndivEdu(String indivEdu) {
        this.indivEdu = indivEdu;
    }

    public String getIdCardAddress() {
        return idCardAddress;
    }

    public void setIdCardAddress(String idCardAddress) {
        this.idCardAddress = idCardAddress;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }
}
