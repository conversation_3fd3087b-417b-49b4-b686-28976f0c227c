package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum LoanPurpose {
    SHOPPING("购物"),
    HEALTH("医疗健康"),
    TOUR("旅游度假"),
    MARRIAGE("婚庆"),
    DECORATION("店面装修"),
    OTHER("其他"),
    TRANSPORTATION("交通通讯"),
    EDUCATION("教育培训");

    private final String desc;

    LoanPurpose(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static LoanPurpose LoanPurpose(String type) {
        return switch (type) {
            case "01" ->LoanPurpose.SHOPPING;
            case "02" ->LoanPurpose.HEALTH;
            case "03" -> LoanPurpose.TOUR;
            case "04" -> LoanPurpose.MARRIAGE;
            case "05" ->LoanPurpose.DECORATION;
            case "06" -> LoanPurpose.OTHER;
            case "07" ->LoanPurpose.EDUCATION;
            default -> LoanPurpose.OTHER;
        };
    }

    public static LoanPurpose LoanPurposeByString(String type) {
        return switch (type) {
            case "运动健身" ->LoanPurpose.HEALTH;
            case "电商购物","日常消费","生活开销" ->LoanPurpose.SHOPPING;
            case "学习进修","学艺术班","技能培训" -> LoanPurpose.EDUCATION;
            default -> LoanPurpose.OTHER;
        };
    }
}
