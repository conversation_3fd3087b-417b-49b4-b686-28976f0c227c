package com.maguo.loan.cash.flow.entrance.bairong.dto.repay;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName RepayInfoList
 * <AUTHOR>
 * @Description 账单信息
 * @Date 2025/9/11 16:50
 * @Version v1.0
 **/
@Data
public class RepayInfoList {
    /**
     * 渠道还款流水号
     */
    @NotBlank(message = "渠道还款流水号不能为空")
    private String outBatchRepaymentSeq;

    /**
     * 资金方放款流水号
     */
    private String loanSeq;

    /**
     * 资金方借据号
     * 类型：String(50)，必填
     */
    @NotBlank(message = "资金方借据号不能为空")
    private String loanNo;

    /**
     * 还款模式
     * 取值范围：0-当期还款，1-逾期还款，2-提前结清，6-提前还当期（未到期按期还款）
     */
    @NotBlank(message = "还款模式不能为空")
    private String repaymentMode;

    /**
     * 还款期次
     * 说明：还款模式为1（逾期还款）、6（提前还当期）时必录；还多期时用逗号隔开（如：3,4,5）
     */
    @NotNull(message = "还款期次不能为空")
    private String period;

    /**
     * 本次总计还款金额
     */
    @NotNull(message = "本次总计还款金额不能为空")
    private BigDecimal curentTotalOdAmt;

    /**
     * 扣款模式
     * 取值范围：1-资金方发起扣款动作，2-资金方仅入账
     */
    @NotBlank(message = "扣款模式不能为空")
    private String cutMode;

    /**
     * 客户实际还款日期
     */
    private String custSetlDt;

}
