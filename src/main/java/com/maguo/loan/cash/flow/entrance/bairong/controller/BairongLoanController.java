package com.maguo.loan.cash.flow.entrance.bairong.controller;

import com.maguo.loan.cash.flow.entrance.bairong.service.BairongService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 百融放款接口
 * @Author: hehangzheng
 * @Date: 2025-9-10 下午 02:35
 */
@RestController
@RequestMapping("bairong")
public class BairongLoanController {

    private static final Logger logger = LoggerFactory.getLogger(BairongLoanController.class);

    @Autowired
    private BairongService baiRongService;

}
