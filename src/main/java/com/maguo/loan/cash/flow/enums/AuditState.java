package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public enum AuditState {
    INIT,
    SUSPEND,
    AUDITING,
    PASS() {
        @Override
        public boolean isFinal() {
            return true;
        }
    },
    REJECT() {
        @Override
        public boolean isFinal() {
            return true;
        }
    };


    public boolean isFinal() {
        return false;
    }
}
