# 路由系统梳理文档

## 📋 概述

这个项目中包含了多种类型的路由系统，主要分为以下几个层面：

1. **前端路由系统** - Vue.js 单页应用路由
2. **后端API路由** - Spring Boot REST API 路由
3. **业务路由系统** - 金融业务流量路由配置
4. **消息路由系统** - RabbitMQ 消息队列路由

## 🎯 1. 前端路由系统 (Vue Router)

### 1.1 基础路由配置

<augment_code_snippet path="src/router/routers.js" mode="EXCERPT">
````javascript
export const constantRouterMap = [
  { path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/home'], resolve),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true }
      }
    ]
  }
]
````
</augment_code_snippet>

### 1.2 动态路由加载机制

<augment_code_snippet path="src/router/index.js" mode="EXCERPT">
````javascript
export const loadMenus = (next, to) => {
  buildMenus().then(res => {
    const sdata = JSON.parse(JSON.stringify(res))
    const rdata = JSON.parse(JSON.stringify(res))
    const sidebarRoutes = filterAsyncRouter(sdata)
    const rewriteRoutes = filterAsyncRouter(rdata, false, true)
    rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })

    store.dispatch('GenerateRoutes', rewriteRoutes).then(() => { // 存储路由
      router.addRoutes(rewriteRoutes) // 动态添加可访问路由表
      next({ ...to, replace: true })
    })
    store.dispatch('SetSidebarRouters', sidebarRoutes)
  })
}
````
</augment_code_snippet>

**特点：**
- 基于权限的动态路由加载
- 支持路由守卫和权限验证
- 菜单与路由分离管理

## 🔧 2. 后端API路由系统

### 2.1 系统管理路由

<augment_code_snippet path="cash-manage-system/src/main/java/com/jinghang/cash/modules/system/rest/RoleController.java" mode="EXCERPT">
````java
@RestController
@RequiredArgsConstructor
@Api(tags = "系统：角色管理")
@RequestMapping("/api/roles")
public class RoleController {
    
    @ApiOperation("获取单个role")
    @GetMapping(value = "/{id}")
    @PreAuthorize("@el.check('roles:list')")
    public ResponseEntity<RoleDto> findRoleById(@PathVariable Long id){
        return new ResponseEntity<>(roleService.findById(id), HttpStatus.OK);
    }
````
</augment_code_snippet>

### 2.2 业务API路由

<augment_code_snippet path="capital-core/src/main/java/com/jinghang/capital/core/controller/LoanController.java" mode="EXCERPT">
````java
@RestController
@RequestMapping("loan")
public class LoanController implements LoanService {
    
    @Override
    public RestResult<LoanResultDto> loan(LoanApplyDto apply) {
        logger.info("begin loan apply: outerLoanId: {}, outCreditId: {}, creditId: {}", 
            apply.getSysId(), apply.getSysCreditId(), apply.getCreditId());
        LoanApplyVo applyVo = ApiLoanConvert.INSTANCE.toLoanApplyVo(apply);
        LoanResultVo result = manageService.loanApply(applyVo);
        return RestResult.success(resultDto);
    }
````
</augment_code_snippet>

**API路由分类：**
- `/api/roles` - 角色管理
- `/api/menus` - 菜单管理  
- `/api/dict` - 字典管理
- `/loan` - 放款服务
- `/credit` - 授信服务
- `/protocol` - 协议服务

## 💰 3. 业务路由系统 (核心)

### 3.1 流量路由配置

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/entity/FlowRouteConfig.java" mode="EXCERPT">
````java
/**
 * 流量路由
 */
@Entity
@Table(name = "flow_route_config")
public class FlowRouteConfig extends BaseEntity {
    
    /**
     * 流量id
     */
    @Column(name = "flow_id")
    private String flowId;

    /**
     * 资金IDx
     */
    @Column(name = "capital_id")
    private String capitalId;

    /**
     * 资金优先级
     */
    @Column(name = "priority")
    private Integer priority;
````
</augment_code_snippet>

### 3.2 权益路由配置

<augment_code_snippet path="cash-manage-system/src/main/java/com/jinghang/cash/pojo/RightsRouteConfig.java" mode="EXCERPT">
````java
/**
 * 权益路由配置
 */
@TableName(value ="rights_route_config")
@Data
public class RightsRouteConfig implements Serializable {
    
    /**
     * 权益供应商
     */
    @Enumerated(EnumType.STRING)
    private RightsSupplier rightsSupplier;

    /**
     * 支付通道编码
     */
    private String paymentChannelCode;

    /**
     * 优先级
     */
    private Integer priority;
````
</augment_code_snippet>

### 3.3 资金路由配置

<augment_code_snippet path="cash-manage-system/src/main/java/com/jinghang/cash/pojo/CapitalConfig.java" mode="EXCERPT">
````java
/**
 * 资方配置
 */
@TableName(value = "capital_config")
@Data
public class CapitalConfig implements Serializable {
    
    /**
     * 资金方
     */
    @TableField(value = "bank_channel")
    private String bankChannel;

    /**
     * 资方授信日限额
     */
    @TableField(value = "credit_day_limit")
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    @TableField(value = "loan_day_limit")
    private BigDecimal loanDayLimit;
````
</augment_code_snippet>

## 📨 4. 消息路由系统 (RabbitMQ)

### 4.1 路由键定义

<augment_code_snippet path="src/main/java/com/maguo/loan/cash/flow/config/RabbitConfig.java" mode="EXCERPT">
````java
public interface RoutingKeys {
    
    String RISK_APPLY = "risk.apply";
    String RISK_APPLY_DELAY = "risk.apply.delay";
    String RISK_LOAN_APPLY = "risk.loan.apply";
    
    String CREDIT_ROUTE_APPLY = "credit.route.apply";
    String CREDIT_ROUTE_RESULT = "credit.route.result";
    
    String SIGN_APPLY = "sign.apply";
    String SIGN_QUERY = "sign.query";
````
</augment_code_snippet>

**消息路由分类：**
- `risk.*` - 风控相关消息路由
- `credit.*` - 授信相关消息路由  
- `loan.*` - 放款相关消息路由
- `repay.*` - 还款相关消息路由
- `sign.*` - 签约相关消息路由

## 🔄 5. 路由系统工作流程

### 5.1 业务路由决策流程

```mermaid
graph TD
    A[用户请求] --> B[流量入口]
    B --> C[流量路由配置]
    C --> D{检查流量状态}
    D -->|启用| E[获取资金路由列表]
    D -->|禁用| F[拒绝请求]
    E --> G[按优先级排序]
    G --> H[资金方可用性检查]
    H --> I{资金方可用?}
    I -->|是| J[路由到资金方]
    I -->|否| K[尝试下一个资金方]
    K --> H
    J --> L[业务处理]
```

### 5.2 权限路由验证流程

```mermaid
graph TD
    A[前端路由请求] --> B[路由守卫]
    B --> C{用户已登录?}
    C -->|否| D[跳转登录页]
    C -->|是| E{角色权限检查}
    E -->|无权限| F[跳转401页面]
    E -->|有权限| G[动态加载菜单]
    G --> H[生成路由表]
    H --> I[渲染页面]
```

## 📊 6. 路由配置管理

### 6.1 流量路由管理接口

- **查询流量配置**: `GET /api/flow/config`
- **更新流量路由**: `PUT /api/flow/route/config`
- **启用/禁用流量**: `PUT /api/flow/enable`

### 6.2 资金路由管理接口

- **查询资金配置**: `GET /api/capital/config`
- **更新资金配置**: `PUT /api/capital/config`
- **启用/禁用资金方**: `PUT /api/capital/enable`

### 6.3 权益路由管理接口

- **查询权益路由**: `GET /api/rights/route/config`
- **保存权益路由**: `POST /api/rights/route/config`

## 🎯 7. 理解要点

### 7.1 前端路由
- **静态路由**: 登录、404等基础页面
- **动态路由**: 基于用户权限动态加载的业务页面
- **路由守卫**: 登录验证、权限检查

### 7.2 后端路由  
- **RESTful设计**: 遵循REST规范的API设计
- **权限控制**: 基于注解的方法级权限控制
- **统一响应**: 标准化的API响应格式

### 7.3 业务路由
- **流量分发**: 根据配置将请求路由到不同资金方
- **优先级控制**: 支持按优先级进行路由选择
- **动态配置**: 支持实时修改路由配置

### 7.4 消息路由
- **异步处理**: 通过消息队列实现异步业务处理
- **解耦设计**: 各业务模块通过消息进行松耦合通信
- **可靠传输**: 支持消息重试和延迟处理

## 🔧 8. 配置建议

1. **前端路由**: 合理设计路由层级，避免过深嵌套
2. **API路由**: 保持RESTful风格，统一命名规范
3. **业务路由**: 定期检查路由配置，确保业务连续性
4. **消息路由**: 监控消息队列状态，及时处理异常

这个路由系统设计体现了现代金融系统的复杂性和灵活性，通过多层次的路由机制实现了业务的高可用和可扩展性。
