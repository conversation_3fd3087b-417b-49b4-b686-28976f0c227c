# 🚀 Flow项目开发实战指南

## 📋 概述

本文档基于JH-Loan-Cash-Flow项目的实际开发经验，提供具体的开发场景、常见问题解决方案和最佳实践，帮助开发者快速上手并高效开发。

## 🎯 常见开发场景

### 1. 🆕 新增银行渠道

#### 场景描述
需要接入一个新的银行资方，支持授信和放款功能。

#### 开发步骤

**Step 1: 添加银行渠道枚举**
```java
// 在 BankChannel 枚举中添加新渠道
public enum BankChannel {
    // 现有渠道...
    NEW_BANK("新银行直连", "NEW_BANK");
}
```

**Step 2: 配置资方信息**
```sql
-- 在 capital_config 表中添加配置
INSERT INTO capital_config (
    bank_channel, credit_day_limit, loan_day_limit,
    credit_start_time, credit_end_time,
    loan_start_time, loan_end_time,
    periods_range, ages_range, single_amt_range,
    enabled, created_time, updated_time
) VALUES (
    'NEW_BANK', 5000000, 3000000,
    '09:00:00', '21:00:00',
    '09:00:00', '17:00:00',
    '3,6,12', '18,65', '1000,50000',
    'Y', NOW(), NOW()
);
```

**Step 3: 配置路由关系**
```sql
-- 在 flow_route_config 表中添加路由配置
INSERT INTO flow_route_config (
    flow_id, capital_id, priority, enabled,
    created_time, updated_time
) VALUES (
    'flow_id_xxx', 'capital_id_xxx', 1, 'Y',
    NOW(), NOW()
);
```

**Step 4: 实现银行特定逻辑**
```java
// 在 CreditService 中添加银行特定处理
private CreditApplyDto wrapApplyCreditParam(Credit credit, Order order, List<UserFile> userFiles) {
    // ... 通用逻辑
    
    // 新银行特定逻辑
    if (BankChannel.NEW_BANK == credit.getBankChannel()) {
        // 新银行特定参数处理
        creditApplyDto.setSpecialParam("new_bank_value");
    }
    
    return creditApplyDto;
}
```

#### 注意事项
- 确保银行渠道编码唯一
- 配置合理的时间窗口和限额
- 测试环境先验证完整流程
- 关注银行特定的业务规则

### 2. 🔄 修改业务流程

#### 场景描述
需要在放款前增加一个额外的风控检查环节。

#### 开发步骤

**Step 1: 定义新的检查服务**
```java
@Service
public class AdditionalRiskCheckService {
    
    public boolean checkAdditionalRisk(String orderId) {
        // 实现额外的风控检查逻辑
        Order order = orderRepository.findById(orderId).orElseThrow();
        
        // 检查逻辑
        boolean riskPass = performRiskCheck(order);
        
        // 记录检查结果
        saveRiskCheckRecord(orderId, riskPass);
        
        return riskPass;
    }
}
```

**Step 2: 修改放款流程**
```java
// 在 LoanService.apply 方法中添加检查
public Loan apply(String creditId) {
    // ... 现有逻辑
    
    // 新增额外风控检查
    if (!additionalRiskCheckService.checkAdditionalRisk(order.getId())) {
        loanCommonService.loanFail(loan, "额外风控检查未通过");
        return loan;
    }
    
    // ... 继续原有流程
}
```

**Step 3: 添加配置开关**
```java
@Value("${business.additional.risk.check.enabled:false}")
private boolean additionalRiskCheckEnabled;

// 在检查前判断开关
if (additionalRiskCheckEnabled && !additionalRiskCheckService.checkAdditionalRisk(order.getId())) {
    // 检查失败处理
}
```

#### 最佳实践
- 使用配置开关控制新功能
- 保持向后兼容性
- 充分的单元测试覆盖
- 详细的日志记录

### 3. 📊 添加新的统计报表

#### 场景描述
需要添加一个按银行渠道统计放款成功率的报表。

#### 开发步骤

**Step 1: 创建统计VO**
```java
public class LoanSuccessRateVo {
    private BankChannel bankChannel;
    private String bankChannelName;
    private Long totalCount;
    private Long successCount;
    private BigDecimal successRate;
    private LocalDate statDate;
    
    // getters and setters
}
```

**Step 2: 创建Repository查询方法**
```java
@Repository
public interface LoanStatRepository extends JpaRepository<Loan, String> {
    
    @Query(value = """
        SELECT 
            l.bank_channel as bankChannel,
            COUNT(*) as totalCount,
            SUM(CASE WHEN l.loan_state = 'SUCCEED' THEN 1 ELSE 0 END) as successCount
        FROM loan l 
        WHERE DATE(l.created_time) = :statDate
        GROUP BY l.bank_channel
        """, nativeQuery = true)
    List<Object[]> findLoanSuccessRateByDate(@Param("statDate") LocalDate statDate);
}
```

**Step 3: 创建统计服务**
```java
@Service
public class LoanStatService {
    
    public List<LoanSuccessRateVo> getLoanSuccessRate(LocalDate statDate) {
        List<Object[]> results = loanStatRepository.findLoanSuccessRateByDate(statDate);
        
        return results.stream().map(result -> {
            LoanSuccessRateVo vo = new LoanSuccessRateVo();
            vo.setBankChannel(BankChannel.valueOf((String) result[0]));
            vo.setBankChannelName(vo.getBankChannel().getDesc());
            vo.setTotalCount(((Number) result[1]).longValue());
            vo.setSuccessCount(((Number) result[2]).longValue());
            vo.setSuccessRate(calculateSuccessRate(vo.getSuccessCount(), vo.getTotalCount()));
            vo.setStatDate(statDate);
            return vo;
        }).collect(Collectors.toList());
    }
    
    private BigDecimal calculateSuccessRate(Long successCount, Long totalCount) {
        if (totalCount == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(successCount)
            .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100));
    }
}
```

**Step 4: 创建Controller接口**
```java
@RestController
@RequestMapping("/manage/stat")
public class LoanStatController {
    
    @Autowired
    private LoanStatService loanStatService;
    
    @PostMapping("/loanSuccessRate")
    public RestResult<List<LoanSuccessRateVo>> getLoanSuccessRate(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate statDate) {
        List<LoanSuccessRateVo> result = loanStatService.getLoanSuccessRate(statDate);
        return RestResult.success(result);
    }
}
```

## 🐛 常见问题及解决方案

### 1. 消息队列积压问题

#### 问题现象
```
监控告警：授信申请队列积压超过1000条消息
```

#### 排查步骤
```bash
# 1. 查看队列状态
rabbitmqctl list_queues name messages

# 2. 查看消费者状态
rabbitmqctl list_consumers

# 3. 查看应用日志
tail -f /home/<USER>/logs/cash-flow/cash-flow.log | grep "授信申请异常"
```

#### 解决方案
```java
// 1. 增加消费者并发数
@RabbitListener(queues = RabbitConfig.Queues.CREDIT_APPLY, 
               concurrency = "5-10")  // 增加并发消费者
public void listenCreditApply(Message message, Channel channel)

// 2. 优化业务处理逻辑
public void bankCredit(String creditId) {
    try {
        // 添加超时控制
        CompletableFuture.supplyAsync(() -> {
            return finCreditService.credit(creditApplyDto);
        }).get(30, TimeUnit.SECONDS);  // 30秒超时
    } catch (TimeoutException e) {
        // 超时处理
        logger.error("授信申请超时, creditId: {}", creditId);
        // 重新入队或标记失败
    }
}

// 3. 添加熔断机制
@HystrixCommand(fallbackMethod = "creditFallback")
public RestResult<CreditResultDto> credit(CreditApplyDto dto) {
    return finCreditService.credit(dto);
}

public RestResult<CreditResultDto> creditFallback(CreditApplyDto dto) {
    // 降级处理
    return RestResult.fail("服务暂时不可用");
}
```

### 2. 数据库死锁问题

#### 问题现象
```
Deadlock found when trying to get lock; try restarting transaction
```

#### 排查步骤
```sql
-- 查看当前锁等待情况
SELECT * FROM information_schema.INNODB_LOCKS;

-- 查看锁等待关系
SELECT * FROM information_schema.INNODB_LOCK_WAITS;

-- 查看事务状态
SELECT * FROM information_schema.INNODB_TRX;
```

#### 解决方案
```java
// 1. 统一锁顺序
@Transactional
public void updateOrderAndCredit(String orderId, String creditId) {
    // 按照固定顺序获取锁，避免死锁
    // 先锁order表，再锁credit表
    Order order = orderRepository.findByIdForUpdate(orderId);
    Credit credit = creditRepository.findByIdForUpdate(creditId);
    
    // 业务处理
    updateOrder(order);
    updateCredit(credit);
}

// 2. 减少事务范围
@Transactional
public void processOrder(String orderId) {
    // 将大事务拆分为小事务
    updateOrderStatus(orderId);
}

@Transactional
public void processCredit(String creditId) {
    updateCreditStatus(creditId);
}

// 3. 使用乐观锁
@Entity
public class Order {
    @Version
    private Long version;  // 版本号字段
}

// 更新时检查版本号
public void updateOrder(Order order) {
    try {
        orderRepository.save(order);
    } catch (OptimisticLockingFailureException e) {
        // 版本冲突，重试或提示用户
        throw new BizException("数据已被其他用户修改，请刷新后重试");
    }
}
```

### 3. 内存泄漏问题

#### 问题现象
```
应用内存使用率持续上升，最终导致OOM
```

#### 排查步骤
```bash
# 1. 生成堆转储文件
jmap -dump:format=b,file=heap.hprof <pid>

# 2. 使用MAT分析工具分析堆转储
# 查看内存占用最大的对象
# 分析GC Root引用链

# 3. 查看GC日志
jstat -gc <pid> 5s
```

#### 解决方案
```java
// 1. 及时关闭资源
@Service
public class FileProcessService {
    
    public void processFile(String filePath) {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            // 处理文件
        } finally {
            if (fis != null) {
                try {
                    fis.close();  // 确保资源关闭
                } catch (IOException e) {
                    logger.error("关闭文件流异常", e);
                }
            }
        }
    }
    
    // 更好的方式：使用try-with-resources
    public void processFileWithTryWithResources(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            // 处理文件
        } catch (IOException e) {
            logger.error("处理文件异常", e);
        }
    }
}

// 2. 避免大对象长时间持有
@Service
public class DataProcessService {
    
    // 避免在类成员变量中持有大对象
    // private List<LargeObject> largeObjects; // 不推荐
    
    public void processLargeData() {
        List<LargeObject> largeObjects = loadLargeData();
        try {
            // 处理数据
            processData(largeObjects);
        } finally {
            // 及时清理
            largeObjects.clear();
            largeObjects = null;
        }
    }
}

// 3. 合理使用缓存
@Service
public class CacheService {
    
    // 使用有界缓存，避免无限增长
    private final Cache<String, Object> cache = Caffeine.newBuilder()
        .maximumSize(10000)  // 最大缓存数量
        .expireAfterWrite(Duration.ofHours(1))  // 1小时过期
        .build();
    
    public Object getCachedData(String key) {
        return cache.get(key, this::loadData);
    }
}
```

### 4. 接口响应慢问题

#### 问题现象
```
接口响应时间超过5秒，用户体验差
```

#### 排查步骤
```java
// 1. 添加性能监控
@RestController
public class OrderController {
    
    @PostMapping("/apply")
    public RestResult<String> apply(@RequestBody OrderApplyRequest request) {
        long startTime = System.currentTimeMillis();
        try {
            // 业务处理
            String result = orderService.apply(request);
            return RestResult.success(result);
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("订单申请接口耗时: {}ms", endTime - startTime);
        }
    }
}

// 2. 数据库查询优化
@Repository
public interface OrderRepository extends JpaRepository<Order, String> {
    
    // 添加索引，优化查询
    @Query("SELECT o FROM Order o WHERE o.userId = :userId AND o.orderState = :state")
    List<Order> findByUserIdAndState(@Param("userId") String userId, 
                                   @Param("state") OrderState state);
}

// 3. 异步处理
@Service
public class OrderService {
    
    @Async("taskExecutor")
    public CompletableFuture<Void> processOrderAsync(String orderId) {
        // 异步处理耗时操作
        processOrder(orderId);
        return CompletableFuture.completedFuture(null);
    }
}
```

#### 解决方案
```java
// 1. 数据库优化
-- 添加索引
CREATE INDEX idx_order_user_state ON `order`(user_id, order_state);
CREATE INDEX idx_credit_order_state ON credit(order_id, state);

// 2. 缓存热点数据
@Service
public class ConfigService {
    
    @Cacheable(value = "capitalConfigSlave", key = "#bankChannel")
    public CapitalConfig getCapitalConfig(BankChannel bankChannel) {
        return capitalConfigRepository.findByBankChannel(bankChannel);
    }
}

// 3. 分页查询大数据量
@Service
public class OrderQueryService {
    
    public PageInfo<OrderVo> queryOrders(OrderQueryRequest request) {
        // 使用分页查询，避免一次性加载大量数据
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<Order> orders = orderRepository.findByConditions(request);
        return new PageInfo<>(orders);
    }
}

// 4. 连接池优化
# application.yml
spring:
  datasource:
    druid:
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
```

## 🔧 开发工具和技巧

### 1. 本地调试技巧

#### 配置本地环境
```yaml
# application-local.yml
spring:
  profiles:
    active: local
  datasource:
    url: *******************************************
    username: root
    password: 123456
  redis:
    host: localhost
    port: 6379
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest

# 开启调试日志
logging:
  level:
    com.maguo.loan.cash.flow: DEBUG
    org.springframework.amqp: DEBUG
```

#### 使用Mock服务
```java
@Profile("local")
@Service
public class MockFinCreditService implements FinCreditService {
    
    @Override
    public RestResult<CreditResultDto> credit(CreditApplyDto dto) {
        // 本地调试时返回模拟数据
        CreditResultDto result = new CreditResultDto();
        result.setCreditId("mock_credit_" + System.currentTimeMillis());
        result.setStatus(ProcessState.SUCCEED);
        return RestResult.success(result);
    }
}
```

### 2. 单元测试最佳实践

```java
@SpringBootTest
@Transactional
@Rollback
public class OrderServiceTest {
    
    @Autowired
    private OrderService orderService;
    
    @MockBean
    private CreditService creditService;
    
    @Test
    public void testOrderApply() {
        // Given
        String orderId = "test_order_001";
        Order order = createTestOrder(orderId);
        orderRepository.save(order);
        
        // Mock依赖服务
        when(creditService.apply(any(), any())).thenReturn(true);
        
        // When
        orderService.apply(orderId, WhetherState.Y);
        
        // Then
        Order updatedOrder = orderRepository.findById(orderId).orElseThrow();
        assertEquals(WhetherState.Y, updatedOrder.getOrderSubmitState());
        
        // 验证Mock调用
        verify(creditService, times(1)).apply(any(), any());
    }
    
    private Order createTestOrder(String orderId) {
        Order order = new Order();
        order.setId(orderId);
        order.setUserId("test_user");
        order.setOrderState(OrderState.AUDIT_PASS);
        order.setLoanCardId("test_card");
        return order;
    }
}
```

### 3. 性能测试

```java
@Component
public class PerformanceTestUtil {
    
    public void testConcurrentOrderApply(int threadCount, int requestCount) {
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(requestCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < requestCount; i++) {
            executor.submit(() -> {
                try {
                    // 执行订单申请
                    orderService.apply("test_order_" + Thread.currentThread().getId(), WhetherState.Y);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    logger.error("订单申请失败", e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await();
            long endTime = System.currentTimeMillis();
            
            logger.info("性能测试结果:");
            logger.info("总请求数: {}", requestCount);
            logger.info("成功数: {}", successCount.get());
            logger.info("失败数: {}", failCount.get());
            logger.info("总耗时: {}ms", endTime - startTime);
            logger.info("平均耗时: {}ms", (endTime - startTime) / requestCount);
            logger.info("QPS: {}", requestCount * 1000 / (endTime - startTime));
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            executor.shutdown();
        }
    }
}
```

## 📚 学习资源推荐

### 技术文档
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis Plus官方文档](https://baomidou.com/)
- [RabbitMQ官方文档](https://www.rabbitmq.com/documentation.html)
- [Redis官方文档](https://redis.io/documentation)

### 开发工具
- **IDEA插件**: Lombok, MyBatis Log, Alibaba Java Coding Guidelines
- **数据库工具**: Navicat, DataGrip, DBeaver
- **API测试**: Postman, Insomnia
- **性能分析**: JProfiler, VisualVM, Arthas

### 监控工具
- **APM**: SkyWalking, Pinpoint
- **日志分析**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **指标监控**: Prometheus + Grafana
- **链路追踪**: Zipkin, Jaeger

---

## 🎯 总结

通过本实战指南，你可以：

1. **掌握常见开发场景**: 了解如何新增银行渠道、修改业务流程等
2. **解决常见问题**: 学会排查和解决消息队列积压、数据库死锁等问题
3. **提升开发效率**: 掌握调试技巧、测试方法和性能优化
4. **建立最佳实践**: 形成规范的开发习惯和代码质量意识

建议结合实际项目需求，逐步实践本指南中的内容，在实践中不断提升技术能力。
