# 🎯 JH-Loan-Cash 系统学习路径指南

## 📋 学习目标

通过系统化的学习路径，帮助新接手的开发者快速理解和掌握JH-Loan-Cash金融贷款系统，能够独立进行开发和维护工作。

## 🗓️ 学习计划总览

| 阶段 | 时间 | 重点内容 | 预期目标 |
|------|------|----------|----------|
| 第一阶段 | 1-2天 | 整体架构理解 | 掌握系统全貌和业务流程 |
| 第二阶段 | 2-3天 | 数据模型深入 | 理解核心实体和数据关系 |
| 第三阶段 | 2-3天 | 配置体系学习 | 掌握系统配置和路由机制 |
| 第四阶段 | 3-5天 | 代码实践 | 能够阅读和修改核心代码 |
| 第五阶段 | 2-3天 | 消息队列与事件 | 理解异步处理机制 |
| 第六阶段 | 1-2天 | 监控与运维 | 掌握系统监控和问题排查 |

---

## 📚 第一阶段：理解整体架构 (1-2天)

### 🎯 学习目标
- 理解微服务架构设计
- 掌握三大核心服务的职责
- 熟悉完整的业务流程

### 📖 学习内容

#### 1.1 微服务架构理解
```
jh-loan-cash/
├── jh-loan-cash-flow/      # 🔄 业务流程服务
├── jh-loan-cash-capital/   # 💰 资方对接服务  
└── jh-loan-cash-manage/    # 🖥️ 后台管理服务
```

**重点理解**：
- **jh-loan-cash-flow**: 核心业务逻辑，处理订单、风控、授信、放款流程
- **jh-loan-cash-capital**: 与银行等资方系统对接，处理资方相关业务
- **jh-loan-cash-manage**: 后台管理界面，提供运营人员使用的管理功能

#### 1.2 业务流程梳理
```mermaid
graph TD
    A[用户申请] --> B[风控审核]
    B --> C{风控结果}
    C -->|通过| D[订单路由]
    C -->|拒绝| E[订单结束]
    D --> F[资方授信]
    F --> G{授信结果}
    G -->|通过| H[协议签署]
    G -->|拒绝| I[路由下一资方]
    H --> J[放款申请]
    J --> K[生成还款计划]
    K --> L[放款成功]
```

#### 1.3 关键概念理解
- **订单路由**: 根据用户画像和资方规则自动分配资方
- **风控审核**: 内部风控 + 外部风控系统
- **授信流程**: 向资方申请用户授信额度
- **协议签署**: 电子协议签署流程
- **放款处理**: 资方放款到用户银行卡

### ✅ 阶段检验
- [ ] 能够画出完整的业务流程图
- [ ] 理解三个服务的职责划分
- [ ] 掌握订单的生命周期状态

---

## 📊 第二阶段：深入数据模型 (2-3天)

### 🎯 学习目标
- 理解核心实体类的设计
- 掌握数据库表结构和关联关系
- 熟悉数据访问层的实现

### 📖 学习内容

#### 2.1 核心实体类关系
```java
// 主要业务实体关联关系
User (用户) 
  ↓ 1:N
Order (订单) 
  ↓ 1:N  
Credit (授信)
  ↓ 1:1
Loan (借据)
  ↓ 1:N
RepayPlan (还款计划)
```

#### 2.2 重点实体类学习

**Order (订单表)**
```java
- id: 订单ID
- userId: 用户ID  
- orderState: 订单状态 (INIT/AUDIT_PASS/CREDITING/LOAN_PASS等)
- applyAmount: 申请金额
- applyPeriods: 申请期数
- bankChannel: 银行渠道
- flowChannel: 流量渠道
```

**Credit (授信表)**
```java
- id: 授信ID
- orderId: 订单ID
- state: 授信状态 (INIT/PROCESSING/SUCCEED/FAILED)
- creditAmount: 授信金额
- bankChannel: 银行渠道
- creditNo: 资方授信编号
```

**Loan (借据表)**
```java
- id: 借据ID
- creditId: 授信ID
- loanState: 放款状态
- loanAmount: 放款金额
- loanCardId: 放款银行卡ID
- repayCardId: 还款银行卡ID
```

#### 2.3 配置实体类学习

**CapitalConfig (资方配置)**
```java
- bankChannel: 银行渠道
- creditDayLimit: 授信日限额
- loanDayLimit: 放款日限额
- creditStartTime/creditEndTime: 授信时间窗口
- periodsRange: 支持期数范围
```

**FlowRouteConfig (流量路由配置)**
```java
- flowId: 流量ID
- capitalId: 资金ID  
- priority: 优先级
- enabled: 启用状态
```

#### 2.4 数据访问层
- **MyBatis Plus**: 主要ORM框架，支持复杂查询
- **JPA**: 系统管理模块使用
- **多数据源**: 主从分离配置

### ✅ 阶段检验
- [ ] 能够画出核心实体的ER图
- [ ] 理解订单状态流转逻辑
- [ ] 掌握配置表对业务的影响

---

## 🔧 第三阶段：配置体系学习 (2-3天)

### 🎯 学习目标
- 掌握资方配置的作用和配置方法
- 理解智能路由的实现原理
- 学会配置新的银行渠道和流量渠道

### 📖 学习内容

#### 3.1 资方配置详解

**时间控制配置**
```java
// 授信时间窗口
creditStartTime: "09:00:00"
creditEndTime: "21:00:00"

// 放款时间窗口  
loanStartTime: "09:00:00"
loanEndTime: "17:00:00"

// 还款时间窗口
repayStartTime: "06:00:00" 
repayEndTime: "22:00:00"
```

**限额配置**
```java
// 资方日限额
creditDayLimit: 5000000  // 500万
loanDayLimit: 3000000    // 300万

// 单笔限额范围
singleAmtRange: "1000,50000"  // 1千-5万

// 期数范围
periodsRange: "3,6,12"  // 支持3、6、12期
```

#### 3.2 智能路由机制

**路由优先级**
```java
// 路由记录按优先级排序
priority: 1  // 数字越小优先级越高
priority: 2
priority: 3
```

**路由规则检查**
```java
1. 资方启用状态检查
2. 时间窗口检查  
3. 限额检查
4. 用户画像匹配
5. 流量规则匹配
```

**路由失败处理**
```java
1. 当前资方授信失败
2. 自动路由到下一优先级资方
3. 所有资方都失败则订单失败
```

#### 3.3 流量配置管理

**流量渠道配置**
```java
flowChannel: QHYP  // 轻花优品
creditDayAmt: 1000000  // 流量授信日限额
loanDayAmt: 800000     // 流量放款日限额
```

**绑卡渠道配置**
```java
firstProtocolChannel: BAOFU    // 第一绑卡渠道：宝付
secondProtocolChannel: ALLINPAY // 第二绑卡渠道：通联
```

### ✅ 阶段检验
- [ ] 能够配置新的银行渠道
- [ ] 理解路由失败重试机制
- [ ] 掌握时间窗口控制原理

---

## 💻 第四阶段：代码实践 (3-5天)

### 🎯 学习目标
- 能够阅读和理解核心业务代码
- 掌握Spring Boot项目的代码结构
- 学会调试和修改业务逻辑

### 📖 学习内容

#### 4.1 从简单功能开始

**后台管理查询功能**
```java
// 1. Controller层
@PostMapping("/queryOrderInfo")
public RestResult<PageInfo<OrderInfoVo>> queryOrderInfo(@RequestBody OrderInfoReq req)

// 2. Service层  
public PageInfo<OrderInfoVo> queryOrderInfo(OrderInfoReq orderInfoReq)

// 3. Mapper层
Page<OrderInfoVo> queryOrderInfo(OrderInfoReq orderInfoReq)
```

**学习重点**：
- 理解分层架构设计
- 掌握MyBatis Plus的使用
- 学习分页查询的实现

#### 4.2 核心业务代码学习

**订单处理流程 (OrderService)**
```java
// 订单申请
public void apply(String orderId, WhetherState rightsMarking)

// 订单路由
public void orderRoute(Order order)

// 订单取消
public void cancelOtherOrders(Order order)
```

**授信处理流程 (CreditService)**
```java
// 授信申请
public void apply(Order order, String routerId)

// 银行授信
public void bankCredit(String creditId)

// 授信结果查询
public void creditResultQuery(String creditId)
```

**放款处理流程 (LoanService)**
```java
// 放款申请
public Loan apply(String creditId)

// 放款记录处理
public void loanRecordApply(String loanRecordId)

// 放款结果处理
public void loanResultProcess(String loanId)
```

#### 4.3 代码调试技巧

**日志查看**
```java
// 关键业务节点都有详细日志
logger.info("订单路由开始, orderId: {}", orderId);
logger.info("授信申请, creditId: {}", creditId);
logger.error("放款异常, loanId: {}, error: {}", loanId, e.getMessage());
```

**断点调试**
- 在关键业务方法设置断点
- 观察数据流转过程
- 理解业务逻辑分支

### ✅ 阶段检验
- [ ] 能够独立阅读核心业务代码
- [ ] 理解分层架构的设计思想
- [ ] 掌握常用的调试方法

---

## 🚀 第五阶段：消息队列与事件 (2-3天)

### 🎯 学习目标
- 理解RabbitMQ在系统中的作用
- 掌握消息队列的设计模式
- 学会事件驱动编程

### 📖 学习内容

#### 5.1 消息队列架构

**核心交换机设计**
```java
RISK = "risk"           // 风控相关消息
CREDIT = "credit"       // 授信相关消息  
LOAN = "loan"          // 放款相关消息
REPAY = "repay"        // 还款相关消息
SMS = "sms"            // 短信相关消息
```

**队列处理模式**
```java
// 1. 消息发送
mqService.submitCreditApply(creditId);

// 2. 消息监听
@RabbitListener(queues = RabbitConfig.Queues.CREDIT_APPLY)
public void listenCreditApply(Message message, Channel channel)

// 3. 业务处理
creditService.bankCredit(creditId);

// 4. 消息确认
channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
```

#### 5.2 消息重试机制

**重试策略**
```java
// 最大重试次数
private static final int MAX_RETRY_TIMES = 3;

// 重试逻辑
if (isExceedMaxRetry(retryTimes)) {
    // 超过最大重试次数，告警并停止
    warningService.warn("超过最大重试次数");
    return;
}

// 延迟重试
Map<String, Object> headers = new HashMap<>();
headers.put(TRY_TIMES_KEY, retryTimes);
retry.accept(body, headers);
```

#### 5.3 事件驱动架构

**Spring Event使用**
```java
// 1. 事件定义
public class LoanResultEvent extends ApplicationEvent

// 2. 事件发布
eventPublisher.publishEvent(new LoanResultEvent(loanId, loanState));

// 3. 事件监听
@EventListener(LoanResultEvent.class)
public void onApplicationEvent(LoanResultEvent event)
```

### ✅ 阶段检验
- [ ] 理解消息队列的设计原理
- [ ] 掌握消息重试和异常处理
- [ ] 学会使用Spring Event

---

## 📊 第六阶段：监控与运维 (1-2天)

### 🎯 学习目标
- 掌握系统监控和告警机制
- 学会问题排查和日志分析
- 理解系统运维要点

### 📖 学习内容

#### 6.1 告警机制

**WarningService使用**
```java
// 普通告警
warningService.warn("业务异常信息");

// 指定人员告警
warningService.warn("严重异常", "13800138000");

// 异常告警
warningService.warn("系统异常", e);
```

#### 6.2 监控指标

**业务监控**
```java
// 积压量监控
- 风控处理积压量
- 授信处理积压量  
- 放款处理积压量
- 还款处理积压量

// 成功率监控
- 撞库成功率
- 授信成功率
- 放款成功率
```

**系统监控**
```java
// 系统资源
- CPU使用率
- 内存使用率  
- 磁盘使用率
- JVM状态

// 服务状态
- 健康检查
- 服务注册状态
- 数据库连接状态
```

#### 6.3 问题排查

**日志分析**
```java
// 链路追踪
traceId: 12345678 -> 通过traceId追踪完整请求链路

// 关键日志
- 订单状态变更日志
- 异常处理日志  
- 业务流程日志
```

### ✅ 阶段检验
- [ ] 能够配置监控告警
- [ ] 掌握问题排查方法
- [ ] 理解系统运维要点

---

## 💡 学习建议

### 🔧 环境搭建
1. **本地开发环境**
   - JDK 17+
   - MySQL 5.7+
   - Redis 3.0+
   - RabbitMQ 3.8+
   - Apollo配置中心

2. **IDE配置**
   - IDEA + Lombok插件
   - MyBatis Log插件
   - Git版本控制

### 📚 学习方法
1. **理论结合实践**: 边学习边搭建环境实际运行
2. **循序渐进**: 按照学习路径逐步深入
3. **多问多练**: 遇到问题及时沟通和实践
4. **文档记录**: 记录学习过程中的重点和难点

### 🎯 学习重点
1. **业务理解**: 金融业务的特殊性和复杂性
2. **代码质量**: 异常处理、事务管理、数据一致性
3. **系统设计**: 微服务架构、消息队列、配置管理
4. **运维监控**: 日志规范、监控告警、问题排查

---

## 🤝 后续支持

完成学习路径后，你将具备：
- ✅ 独立开发和维护系统功能的能力
- ✅ 理解复杂业务流程的设计思想
- ✅ 掌握微服务架构的实践经验
- ✅ 具备问题排查和系统优化的技能

如有任何学习过程中的问题，随时可以：
1. 查阅项目分析报告文档
2. 参考代码注释和日志
3. 寻求团队成员的帮助
4. 进行代码走读和技术讨论

**祝你学习顺利！🚀**
