# 🔧 技术栈和依赖分析

## 📋 概述

本文档详细分析了JH-Loan-Cash-Capital项目的技术栈、依赖关系和外部系统集成情况，为接手自研提供技术参考。

## 🏗️ 核心技术架构

### 🔧 基础框架
| 技术 | 版本 | 用途 | 模块 |
|------|------|------|------|
| Spring Boot | 3.2.1 | 主框架 | 全部 |
| Spring Cloud | 2023.0.0 | 微服务框架 | 全部 |
| Java | 17 | 运行环境 | 全部 |
| Maven | - | 构建工具 | 全部 |

### 🗄️ 数据访问层
| 技术 | 版本 | 用途 | 模块 |
|------|------|------|------|
| Spring Data JPA | 3.2.1 | ORM框架 | capital-core |
| MyBatis Plus | 3.5.5 | 数据访问 | capital-batch |
| MySQL Connector | 8.x | 数据库驱动 | 全部 |
| Redisson | 3.25.2 | Redis客户端 | capital-core |

### 🌐 服务治理
| 技术 | 版本 | 用途 | 模块 |
|------|------|------|------|
| Eureka Client | 2023.0.0 | 服务注册发现 | 全部 |
| OpenFeign | 2023.0.0 | 服务间通信 | 全部 |
| Apollo Client | 2.2.0 | 配置中心 | 全部 |
| Spring AMQP | 3.2.1 | 消息队列 | capital-core |

### ⏰ 任务调度
| 技术 | 版本 | 用途 | 模块 |
|------|------|------|------|
| XXL-Job Core | 2.1.0 | 分布式任务调度 | capital-batch |

## 📦 核心依赖分析

### Capital-Core 模块依赖

#### 🔐 安全和加密
```xml
<!-- 加密解密 -->
<dependency>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcpkix-jdk18on</artifactId>
    <version>1.74</version>
</dependency>
```

#### 📁 文件处理
```xml
<!-- PDF处理 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.27</version>
</dependency>

<!-- 图片处理 -->
<dependency>
    <groupId>net.coobird</groupId>
    <artifactId>thumbnailator</artifactId>
    <version>0.4.20</version>
</dependency>

<!-- 文件压缩 -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-compress</artifactId>
</dependency>
```

#### 🌐 网络通信
```xml
<!-- SFTP客户端 -->
<dependency>
    <groupId>com.github.mwiede</groupId>
    <artifactId>jsch</artifactId>
    <version>0.2.17</version>
</dependency>
```

#### ☁️ 云服务
```xml
<!-- 阿里云OSS -->
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.16.3</version>
</dependency>

<!-- 华为云OBS -->
<dependency>
    <groupId>com.huaweicloud</groupId>
    <artifactId>esdk-obs-java</artifactId>
    <version>3.23.5</version>
</dependency>
```

#### 🏦 银行对接
```xml
<!-- 蚂蚁数科SDK -->
<dependency>
    <groupId>com.antgroup.antchain.openapi</groupId>
    <artifactId>openapi-riskplus</artifactId>
    <version>1.23.5</version>
</dependency>

<dependency>
    <groupId>cn.com.antcloud.api</groupId>
    <artifactId>antcloud-api-sdk</artifactId>
    <version>3.4.0</version>
</dependency>

<!-- CYCFC银行客户端 -->
<dependency>
    <groupId>com.cycfc</groupId>
    <artifactId>cycfc_client</artifactId>
    <version>1.4</version>
</dependency>
```

#### 🛠️ 工具库
```xml
<!-- JSON处理 -->
<dependency>
    <groupId>com.alibaba.fastjson2</groupId>
    <artifactId>fastjson2</artifactId>
    <version>2.0.44</version>
</dependency>

<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>2.0.43</version>
</dependency>

<!-- 对象映射 -->
<dependency>
    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct</artifactId>
    <version>1.5.5.Final</version>
</dependency>

<!-- 工具类 -->
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-core</artifactId>
    <version>5.8.26</version>
</dependency>

<!-- 缓存 -->
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
</dependency>

<!-- 测试数据生成 -->
<dependency>
    <groupId>com.github.javafaker</groupId>
    <artifactId>javafaker</artifactId>
    <version>1.0.2</version>
</dependency>
```

### Capital-Batch 模块依赖

#### 🗄️ 数据访问
```xml
<!-- MyBatis Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.5</version>
</dependency>

<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-generator</artifactId>
    <version>3.5.5</version>
</dependency>
```

#### 📝 模板引擎
```xml
<!-- FreeMarker模板 -->
<dependency>
    <groupId>org.freemarker</groupId>
    <artifactId>freemarker</artifactId>
</dependency>
```

#### 🔧 代码生成
```xml
<!-- Lombok -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.30</version>
    <scope>provided</scope>
</dependency>

<!-- Groovy脚本 -->
<dependency>
    <groupId>org.apache.groovy</groupId>
    <artifactId>groovy</artifactId>
</dependency>
```

## 🔗 外部系统集成

### 🏦 银行系统

#### HXBK (湖消银行)
- **接入方式**: 蚂蚁数科RiskPlus平台
- **认证方式**: AK/SK
- **通信协议**: HTTPS
- **数据格式**: JSON
- **服务时间**: 23:00-03:00

#### CYBK
- **接入方式**: 专用客户端SDK
- **通信协议**: 自定义协议
- **数据格式**: 自定义格式

### 📁 文件存储系统

#### SFTP服务器
- **主机**: sftp-dev.alipay.com
- **端口**: 22
- **认证**: 用户名密码
- **用途**: 合同、身份证、照片等文件存储

#### 阿里云OSS
- **用途**: 文件存储备选方案
- **SDK版本**: 3.16.3

#### 华为云OBS
- **用途**: 文件存储备选方案
- **SDK版本**: 3.23.5

### 🔧 中间件系统

#### Apollo配置中心
- **版本**: 2.2.0
- **用途**: 统一配置管理
- **配置**: `spring.config.import=apollo://`

#### Eureka注册中心
- **用途**: 服务注册与发现
- **配置**: 
  - 心跳间隔: 4秒
  - 过期时间: 12秒
  - 拉取间隔: 5秒

#### RabbitMQ消息队列
- **用途**: 异步消息处理
- **确认模式**: 手动确认

#### Redis缓存
- **客户端**: Redisson 3.25.2
- **用途**: 缓存和分布式锁

#### XXL-Job任务调度
- **版本**: 2.1.0
- **用途**: 分布式任务调度
- **配置**: 需要配置调度中心地址

## 🔍 依赖风险分析

### 🔴 高风险依赖

#### 1. 银行专用SDK
- **风险**: 版本更新可能导致兼容性问题
- **影响**: 直接影响银行对接功能
- **建议**: 建立版本管理和测试机制

#### 2. 第三方云服务SDK
- **风险**: API变更或服务不可用
- **影响**: 文件存储功能受影响
- **建议**: 实现多云备份策略

### 🟡 中等风险依赖

#### 1. 中间件版本
- **风险**: 版本过旧可能存在安全漏洞
- **影响**: 系统安全性和稳定性
- **建议**: 定期升级到稳定版本

#### 2. JSON处理库
- **风险**: FastJSON历史上存在安全问题
- **影响**: 数据序列化安全
- **建议**: 考虑迁移到Jackson

### 🟢 低风险依赖

#### 1. 工具类库
- **风险**: 功能性问题，不影响核心业务
- **影响**: 开发效率
- **建议**: 保持版本更新

## 💡 技术栈优化建议

### 1. 🔄 版本升级
- **Spring Boot**: 考虑升级到最新LTS版本
- **Java**: 保持Java 17，考虑未来升级到Java 21
- **依赖库**: 定期检查和升级依赖版本

### 2. 🔒 安全加固
- **替换FastJSON**: 迁移到Jackson或其他安全的JSON库
- **加密算法**: 使用更现代的加密算法
- **依赖扫描**: 定期进行依赖安全扫描

### 3. 🚀 性能优化
- **缓存策略**: 优化Redis使用方式
- **连接池**: 优化数据库和HTTP连接池配置
- **异步处理**: 增加异步处理能力

### 4. 🔧 架构改进
- **服务拆分**: 考虑进一步的微服务拆分
- **API网关**: 引入API网关统一管理接口
- **监控体系**: 完善监控和链路追踪

## 📋 依赖清单总结

### 核心依赖 (必须)
- Spring Boot 3.2.1
- Spring Cloud 2023.0.0
- MySQL Connector
- Redis (Redisson)

### 业务依赖 (重要)
- 蚂蚁数科SDK
- CYCFC客户端
- XXL-Job

### 工具依赖 (辅助)
- MapStruct
- Hutool
- Apache Commons
- BouncyCastle

### 存储依赖 (可选)
- 阿里云OSS
- 华为云OBS
- JSCH (SFTP)

这个技术栈分析为接手自研提供了全面的技术参考，建议在实施过程中重点关注高风险依赖的管理和优化。
