# HXBK功能开发详细文档

## 功能模块详细说明

### 1. 公共报文体SDK加解密

#### 1.1 统一请求响应格式
- **固定字段结构**: 包含appid（应用标识）、method（业务方法名）、version（接口版本）、timestamp（时间戳）、requestId（请求唯一标识）等标准字段
- **业务数据封装**: 实际业务数据通过AES加密后放入data字段，确保传输安全
- **签名验证机制**: 使用SHA256WithRSA算法对关键参数进行数字签名，防止数据篡改
- **密钥传输**: SecretKey通过RSA公钥加密传输，保证密钥安全性

#### 1.2 新验签方式对接
- **验签流程**: 接收请求 → 解析公共参数 → 构建验签字符串 → RSA验签 → 解密SecretKey → AES解密业务数据
- **签名算法**: 采用SHA256WithRSA算法，确保签名的安全性和不可伪造性
- **参数排序**: 验签时按参数名ASCII码顺序排列，格式为"key1=value1&key2=value2"
- **错误处理**: 验签失败时返回统一错误码和错误信息，便于问题定位

### 2. 授信流程

#### 2.1 核心业务实现
- **服务入口**: HXBKCreditService继承AbstractBankCreditService，实现统一的授信业务接口
- **流程控制**: 授信申请 → 影像件上传 → 接口调用 → 异步结果查询 → 状态更新
- **数据转换**: 通过HXBKDataBuilder构建个人信息、工作信息、居住信息等标准化数据结构
- **异常处理**: 完善的异常捕获和重试机制，确保业务连续性

#### 2.2 数据转换逻辑
- **个人信息转换**: 身份证信息、联系方式、基本属性等字段映射和格式化
- **枚举映射转换**: 婚姻状态（HXBKMarriage）、学历（HXBKEducation）、职业（HXBKOccupation）、行业（HXBKIndustry）等枚举值转换
- **风险数据构建**: 联系人信息、借款金额、利率等风险评估相关数据的JSON格式化
- **地址信息处理**: 省市区街道等地理位置信息的标准化处理

#### 2.3 文件上传处理
- **影像件分类**: 支持身份证正面（ID_HEAD）、身份证反面（ID_NATION）、人脸照片（ID_FACE）三种类型
- **文件打包**: 按类型分别打包为ZIP文件，生成对应的索引文件
- **SFTP上传**: 上传到蚂蚁指定的SFTP目录，按日期分目录存储
- **上传日志**: 记录每次文件上传的详细信息到hxbk_ant_file_log表

#### 2.4 接口调用机制
- **请求构建**: 通过HXBKRequestService统一处理所有对外接口调用
- **AntFinTech SDK**: 使用蚂蚁官方SDK进行接口调用，确保协议兼容性
- **参数填充**: 自动填充公共参数（baseUrl、ak、sk等）
- **响应处理**: 统一的响应解析和错误处理逻辑

### 3. 加解密

#### 3.1 RSA加解密实现
- **密钥管理**: 支持公钥加密、私钥解密的标准RSA操作
- **SecretKey处理**: 使用RSA算法加密AES密钥，解决AES密钥安全传输问题
- **数字签名**: 实现SHA256WithRSA签名和验签功能，确保数据完整性
- **密钥格式**: 支持PKCS#8格式私钥和X.509格式公钥的加载和使用

#### 3.2 AES加解密实现
- **对称加密**: 使用AES算法对业务数据进行快速加解密
- **密钥生成**: 动态生成UUID作为AES密钥，每次请求使用不同密钥
- **加密模式**: 采用AES/ECB/PKCS5Padding模式，确保加密结果的一致性
- **Base64编码**: 加密结果进行Base64编码，便于JSON传输

#### 3.3 工具类设计
- **RSAUtils**: 提供RSA加解密、签名验签的完整功能
- **AESUtils**: 提供AES加解密的便捷方法
- **ParamUtils**: 提供参数解析、响应构建等辅助功能
- **HXBKCryptoUtil**: 封装完整的加解密流程，简化业务调用

### 4. 回调基础接口

#### 4.1 统一入口设计
- **单一接口**: /hxbk/callback/notify处理所有类型的回调请求，简化接口管理
- **方法分发**: 根据请求中的method字段（如dubhe.credit.result.notify）自动路由到对应业务处理器
- **扩展性设计**: 新增回调类型只需添加对应的处理方法，无需修改控制器代码
- **统一响应**: 所有回调都返回统一格式的加密响应

#### 4.2 加解密处理
- **自动解密**: HXBKCallbackApiController自动处理请求解密和验签
- **透明加密**: 业务处理完成后自动构建加密响应返回
- **错误处理**: 统一的错误码映射和异常处理机制
- **日志记录**: 完整的请求响应日志，便于问题排查

#### 4.3 业务分发机制
- **HXBKCallbackService**: 核心分发服务，根据method字段调用具体业务处理器
- **类型安全**: 使用具体的DTO类进行业务数据处理，避免通用Map的类型安全问题
- **示例实现**: 提供CreditResultNotifyRequest、LoanResultNotifyRequest等标准DTO示例

### 5. 授信回调

#### 5.1 结果处理逻辑
- **状态映射**: 将蚂蚁返回的状态码（0-成功/1-失败/2-处理中）转换为系统内部状态
- **数据同步**: 授信结果、额度信息、合同编号等关键数据的同步更新
- **一致性保证**: 与主动查询共用相同的结果处理逻辑，确保数据一致性
- **异常处理**: 完善的异常捕获和业务回滚机制

#### 5.2 业务流程
- **回调接收**: 接收蚂蚁推送的授信结果通知
- **数据解析**: 解析授信额度、合同信息、风险评估结果等
- **状态更新**: 更新授信记录状态，触发后续业务流程
- **通知机制**: 向上游系统发送授信结果通知

### 6. 影像件上传

#### 6.1 上传流程设计
- **文件分类**: 按照蚂蚁要求对影像件进行分类（身份证、人脸照片等）
- **ZIP打包**: 同类型文件打包为ZIP文件，减少传输次数和网络开销
- **SFTP上传**: 使用SFTP协议上传到蚂蚁指定服务器
- **索引文件**: 为每个ZIP文件生成对应的索引文件，记录文件清单

#### 6.2 核心服务实现
- **HXBKImageFileService**: 统一处理所有影像件上传相关功能
- **文件入口**: 提供统一的文件上传入口，支持多种文件类型
- **批次管理**: 生成唯一批次号（CJ+年月日时分秒+4位随机数）进行文件管理
- **路径管理**: 按日期和资金方组织SFTP目录结构

#### 6.3 材料结构定义
- **m_type分类**: 0-风控报告、1-合同、2-图片、3-附件
- **big_code编码**: 00-风控报告、10-合同、20-身份证图片、26-人脸图片、30-附件
- **small_code细分**: 201-身份证人脸面、202-身份证国徽面、212-活体人脸图片
- **文件信息**: 使用实际上传文件名和真实SFTP路径，确保信息准确性

#### 6.4 上传日志记录
- **HXBKAntFileLog**: 记录每次文件上传的详细信息
- **日志内容**: 文件名、上传路径、上传时间、文件大小等关键信息
- **查询支持**: 支持按批次号、文件类型等条件查询上传记录
- **问题排查**: 便于文件上传问题的定位和处理

## 技术实现细节

### 1. 数据转换器设计

#### 1.1 枚举转换实现
- **HXBKMarriage**: 婚姻状态转换，支持未婚(1)、已婚(2)、离异(3)、丧偶(4)等状态映射
- **HXBKEducation**: 学历转换，涵盖小学(1)到博士(8)的完整学历体系
- **HXBKOccupation**: 职业类型转换，按照银监会标准进行职业分类
- **HXBKIndustry**: 行业类型转换，支持国标行业分类代码
- **HXBKContactType**: 联系人关系转换，包括配偶、父母、子女、朋友等关系类型

#### 1.2 数据构建器功能
- **HXBKDataBuilder**: 核心数据构建工具类，提供统一的数据转换接口
- **个人信息构建**: buildPersonalInfo()方法处理身份证、姓名、民族、学历等基础信息
- **工作信息构建**: buildJobInfo()方法处理职业、行业、公司地址等工作相关信息
- **居住信息构建**: buildLiveInfo()方法处理居住地址的省市区街道信息
- **风险数据构建**: buildRiskDataJson()方法构建包含联系人、借款金额、利率的风险评估数据

### 2. 请求服务架构

#### 2.1 HXBKRequestService核心功能
- **通用请求方法**: request()方法封装AntFinTech SDK调用逻辑
- **公共参数填充**: fillCommonParams()自动填充baseUrl、ak、sk等配置参数
- **异常处理机制**: 分类处理JsonProcessingException、HttpException、签名异常等
- **响应数据转换**: 自动将JSON响应转换为指定的响应对象类型

#### 2.2 业务接口实现
- **授信相关**: creditApply()授信申请、creditQuery()授信查询
- **放款相关**: loanApply()放款申请、queryHXBKLoanQuery()放款结果查询
- **还款相关**: repayApply()还款申请、repayQuery()还款查询、repayTrial()还款试算
- **合同相关**: getContract()合同获取、settlementCertificateQuery()结清证明查询

#### 2.3 错误处理策略
- **分层异常处理**: 区分网络异常、业务异常、系统异常等不同层级
- **重试机制**: 对临时性错误实现自动重试，提高系统稳定性
- **日志记录**: 详细记录请求参数、响应数据、异常信息，便于问题排查
- **降级处理**: 关键业务异常时的降级和补偿机制

### 3. 文件管理系统

#### 3.1 SFTP集成
- **连接管理**: 使用连接池管理SFTP连接，提高传输效率
- **目录结构**: 按照"资金方/日期/文件类型"的层级结构组织文件
- **传输优化**: 支持断点续传和并发上传，提高大文件传输效率
- **安全机制**: 使用SSH密钥认证，确保文件传输安全

#### 3.2 文件生命周期管理
- **自动清理**: D-7天目录自动清理机制，避免存储空间浪费
- **版本控制**: 支持文件版本管理，避免重复上传相同文件
- **完整性校验**: 上传后进行MD5校验，确保文件完整性
- **监控告警**: 文件上传失败时的监控告警机制

### 4. 回调处理架构

#### 4.1 控制器设计
- **HXBKCallbackApiController**: 统一回调入口控制器
- **请求解析**: 自动解析加密请求，提取公共参数和业务数据
- **方法路由**: 根据method字段路由到对应的业务处理器
- **响应构建**: 自动构建加密响应，统一返回格式

#### 4.2 业务处理器
- **HXBKCallbackService**: 核心业务分发服务
- **授信回调**: 处理dubhe.credit.result.notify等授信相关回调
- **放款回调**: 处理dubhe.loan.result.notify等放款相关回调
- **还款回调**: 处理dubhe.repay.result.notify等还款相关回调
- **扩展机制**: 支持动态添加新的回调处理器

### 5. 配置管理

#### 5.1 HXBKConfig配置类
- **连接配置**: baseUrl、ak、sk等接口连接参数
- **业务配置**: fundCode、prodNo等业务标识参数
- **文件配置**: SFTP服务器地址、目录路径等文件相关配置
- **加密配置**: 公钥、私钥等加解密相关配置

#### 5.2 环境适配
- **多环境支持**: 开发、测试、生产环境的配置隔离
- **动态配置**: 支持配置热更新，无需重启应用
- **配置校验**: 启动时自动校验配置完整性和有效性
- **安全存储**: 敏感配置信息的加密存储和访问控制

## 业务流程优化

### 1. 授信流程优化
- **简化设计**: 移除协议生成和签章环节，直接在授信申请中包含所有必要信息
- **异步处理**: 采用异步查询模式获取授信结果，提高系统响应速度
- **状态管理**: 完善的授信状态流转和异常处理机制
- **数据一致性**: 确保授信数据在各个系统间的一致性

### 2. 放款流程优化
- **银行卡集成**: 集成银行卡绑定功能，支持多种银行卡类型
- **文件优化**: 优化文件上传流程，减少上传时间和失败率
- **异常处理**: 增强异常处理机制，提高放款成功率
- **监控告警**: 完善的放款监控和异常告警机制

### 3. 还款流程优化
- **试算功能**: 提供还款试算功能，帮助用户了解还款详情
- **多种方式**: 支持主动还款、代扣还款等多种还款方式
- **对账机制**: 完善的还款对账机制，确保资金安全
- **异常处理**: 还款异常时的自动重试和人工干预机制

## 系统集成与对接

### 1. 蚂蚁天枢系统对接
- **SDK集成**: 使用AntFinTech官方SDK，确保接口调用的标准化和稳定性
- **协议适配**: 完全适配蚂蚁天枢系统的接口协议和数据格式要求
- **版本管理**: 支持多版本接口并存，便于系统升级和兼容性维护
- **监控集成**: 集成蚂蚁侧的监控和日志系统，实现端到端的问题追踪

### 2. 内部系统集成
- **统一接口**: 通过AbstractBankCreditService等抽象类实现统一的银行接口
- **数据同步**: 与核心业务系统的实时数据同步机制
- **消息队列**: 使用MQ实现异步处理和系统解耦
- **缓存机制**: 合理使用缓存提高系统性能和响应速度

### 3. 第三方服务集成
- **SFTP服务**: 与蚂蚁SFTP服务器的安全连接和文件传输
- **加密服务**: 集成专业的加解密服务，确保数据安全
- **监控服务**: 集成APM监控，实现全链路性能监控
- **日志服务**: 集成ELK等日志分析系统，便于问题排查

## 质量保证与测试

### 1. 单元测试
- **覆盖率要求**: 核心业务逻辑单元测试覆盖率达到90%以上
- **Mock机制**: 使用Mock框架模拟外部依赖，确保测试独立性
- **边界测试**: 重点测试边界条件和异常情况的处理
- **性能测试**: 关键方法的性能基准测试

### 2. 集成测试
- **接口测试**: 与蚂蚁天枢系统的完整接口调用测试
- **流程测试**: 端到端业务流程的集成测试
- **异常测试**: 各种异常场景下的系统行为测试
- **压力测试**: 高并发场景下的系统稳定性测试

### 3. 安全测试
- **加解密测试**: 加解密算法的正确性和安全性测试
- **签名验证**: 数字签名的生成和验证功能测试
- **数据脱敏**: 敏感数据的脱敏和保护机制测试
- **权限控制**: 接口访问权限和数据访问控制测试

## 运维监控

### 1. 系统监控
- **接口监控**: 所有对外接口的调用成功率、响应时间等关键指标监控
- **业务监控**: 授信、放款、还款等核心业务流程的监控
- **资源监控**: CPU、内存、磁盘、网络等系统资源使用情况监控
- **异常监控**: 系统异常、业务异常的实时监控和告警

### 2. 日志管理
- **结构化日志**: 使用JSON格式的结构化日志，便于自动化分析
- **日志分级**: 按照DEBUG、INFO、WARN、ERROR等级别记录日志
- **敏感信息**: 对敏感信息进行脱敏处理，确保日志安全
- **日志轮转**: 合理的日志轮转策略，避免磁盘空间不足

### 3. 告警机制
- **实时告警**: 关键业务异常的实时告警通知
- **分级告警**: 按照严重程度分级处理告警信息
- **告警收敛**: 避免告警风暴，合理收敛相同类型告警
- **自动恢复**: 部分异常的自动恢复和自愈机制

## 性能优化

### 1. 接口性能优化
- **连接池**: 使用连接池管理HTTP连接，减少连接建立开销
- **缓存策略**: 合理使用缓存减少重复计算和数据库查询
- **异步处理**: 使用异步处理提高系统并发能力
- **批量操作**: 支持批量数据处理，提高处理效率

### 2. 数据库优化
- **索引优化**: 为常用查询字段建立合适的索引
- **查询优化**: 优化SQL查询语句，减少数据库负载
- **连接池**: 合理配置数据库连接池参数
- **读写分离**: 在高并发场景下实现读写分离

### 3. 文件传输优化
- **并发上传**: 支持多文件并发上传，提高传输效率
- **断点续传**: 大文件传输支持断点续传功能
- **压缩传输**: 对文件进行压缩后传输，减少网络带宽占用
- **传输监控**: 实时监控文件传输状态和进度

## 安全机制

### 1. 数据安全
- **传输加密**: 所有数据传输使用HTTPS和自定义加密双重保护
- **存储加密**: 敏感数据在数据库中加密存储
- **访问控制**: 严格的数据访问权限控制机制
- **审计日志**: 完整的数据访问和操作审计日志

### 2. 接口安全
- **身份认证**: 基于AK/SK的接口身份认证机制
- **签名验证**: 使用数字签名防止请求被篡改
- **防重放**: 通过时间戳和随机数防止请求重放攻击
- **限流控制**: 接口访问频率限制，防止恶意调用

### 3. 系统安全
- **权限管理**: 基于角色的访问控制（RBAC）
- **安全审计**: 系统操作的完整审计记录
- **漏洞扫描**: 定期进行安全漏洞扫描和修复
- **应急响应**: 安全事件的应急响应和处理机制
