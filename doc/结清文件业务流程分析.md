# 结清文件业务流程分析

## 📋 业务概述

结清文件业务是针对已结清贷款生成结清证明文件的核心业务流程，主要涉及长银消金(CYBK)等银行渠道的结清证明申请、查询和下载。

## 🔄 核心业务流程

### 1. 整体流程架构

```mermaid
graph TD
    A[定时任务触发] --> B[查询结清贷款]
    B --> C[申请结清证明]
    C --> D[记录申请状态]
    D --> E[定时查询结果]
    E --> F{申请状态}
    F -->|成功| G[下载证明文件]
    F -->|处理中| H[等待下次查询]
    F -->|失败| I[记录失败原因]
    G --> J[保存到OSS]
    J --> K[更新文件记录]
```

### 2. 关键组件说明

#### 2.1 定时任务组件
- **CYBKClearFileApplyJob**: 结清证明申请任务
  - 任务名称: `cybkClearFileApplyJob`
  - 执行频率: 每日执行
  - 默认处理前一天的结清数据

- **CYBKClearFileQueryJob**: 结清证明查询任务
  - 任务名称: `cybkClearFileQueryJob`
  - 执行频率: 每20分钟执行一次
  - 查询范围: 30天内的申请记录

#### 2.2 核心服务类
- **CYBKLoanFileService**: 长银直连文件服务
- **FinVoucherFileService**: 凭证文件服务接口
- **ManageService**: 管理服务统一入口

## 🎯 关键业务点

### 1. 结清数据识别
```java
// 正常按计划结清的贷款
List<String> normalClear = bankLoanReplanRepository.findNormalClear(
    BankChannel.CYBK, RepayType.REPAY, clearStartOfDay, clearNextDayStart);

// 提前结清的贷款  
List<String> advancedClear = bankLoanReplanRepository.findAdvancedClear(
    BankChannel.CYBK, RepayType.REPAY, clearStartOfDay, clearNextDayStart);
```

**关键点**: 
- 区分正常结清和提前结清两种类型
- 按日期范围查询结清记录
- 支持CYBK银行渠道的结清识别

### 2. 申请状态管理
```java
public enum DownloadFileStatusEnum {
    I("初始化"),
    P("处理中"), 
    S("成功"),
    F("失败")
}
```

**关键点**:
- 使用状态机管理申请流程
- 支持失败重试机制
- 记录详细的状态变更日志

### 3. 文件下载与存储
```java
// SFTP下载 -> 临时文件 -> OSS存储
private void getOfSftp(DownloadFileLog downloadFileLog, Loan loan, String sftPath) {
    // 1. 从SFTP下载凭证
    Path tempFile = createTempFile();
    sftp.download(DestMapping.of(sftPath, tempFile.toAbsolutePath().toString()));
    
    // 2. 保存到OSS
    saveLoanFile(downloadFileLog, loan.getCreditId(), FileType.CREDIT_SETTLE_VOUCHER_FILE, sftPath, tempFile);
}
```

**关键点**:
- 采用SFTP -> 临时文件 -> OSS的三级存储架构
- 支持文件格式验证和路径规范化
- 自动清理临时文件避免磁盘占用

### 4. 限流控制
```java
// 每秒调用三十次接口的限流控制
getRateLimiter(CREDIT_SETTLE_VOUCHER_FILE_DOWNLOAD).acquire();
```

**关键点**:
- 使用令牌桶算法控制API调用频率
- 避免对银行接口造成压力
- 保证系统稳定性

### 5. 异步处理机制
```java
@Async("fileProcessThreadPool")
public void downloadCreditSettleVoucher(LocalDate workDate) {
    // 异步处理结清证明下载
}

@Async("fileProcessThreadPool") 
public void sendVoucherApplyRequest(Loan loan, DownloadFileLog downloadFileLog) {
    // 异步发送申请请求
}
```

**关键点**:
- 使用专用线程池处理文件操作
- 避免阻塞主业务流程
- 提高系统并发处理能力

## 🔧 技术实现细节

### 1. 接口调用流程
```java
// 申请结清证明
CYBKClearVoucherApplyRequest request = new CYBKClearVoucherApplyRequest();
request.setOutSeq(loan.getCreditId());
request.setLoanNo(creditFlow.getLoanNo());
request.setBizMode("1"); // 1：非额度类
request.setIdNo(loan.getCustCertNo());

CYBKClearVoucherApplyResponse resp = requestService.clearVoucherApply(request);
```

### 2. 查询结果处理
```java
switch (resp.getStatus()) {
    case CLEAR_VOUCHER_SUCCESS_CODE -> getOfSftp(downloadFileLog, loan, resp.getImgUrl());
    case CLEAR_VOUCHER_FAIL_CODE -> updateDownloadLogFail(downloadFileLog, resp.getStatusDesc());
    case CLEAR_VOUCHER_PROCESS_CODE -> logger.info("处理中，20分钟后再试");
    default -> logger.error("查询失败");
}
```

### 3. 文件路径规范
```java
String ossFilePath = "cybk/voucher/" + dateTimeStr + "/" + loanId + "_clear." + suffixName;
```

## ⚠️ 关键注意事项

### 1. 数据一致性
- 确保结清状态与申请记录的一致性
- 避免重复申请同一笔贷款的结清证明
- 处理并发场景下的状态冲突

### 2. 异常处理
- 网络异常时的重试机制
- SFTP连接失败的容错处理  
- OSS上传失败的回滚策略

### 3. 监控告警
- 申请失败率监控
- 文件下载成功率统计
- 处理时长异常告警

### 4. 性能优化
- 批量处理减少数据库查询
- 合理设置线程池大小
- 优化SFTP连接复用

## 📊 业务指标

### 1. 核心指标
- **申请成功率**: 结清证明申请的成功比例
- **下载成功率**: 文件下载的成功比例  
- **平均处理时长**: 从申请到下载完成的时间
- **重试次数**: 失败后的重试统计

### 2. 监控维度
- 按银行渠道统计
- 按日期范围分析
- 按失败原因分类
- 按文件大小分布

## 🔄 扩展性设计

### 1. 多银行支持
- 抽象化银行接口调用
- 统一的状态管理机制
- 可配置的处理策略

### 2. 文件类型扩展
- 支持多种证明文件类型
- 灵活的文件格式处理
- 可扩展的存储策略

这个结清文件业务流程的核心在于**状态驱动的异步处理模式**，通过定时任务协调申请和查询两个阶段，确保结清证明文件的可靠获取和存储。
